import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { pincode, cartItems } = body;

    // Validate input
    if (!pincode || !cartItems || !Array.isArray(cartItems)) {
      return NextResponse.json(
        { error: 'Invalid request data' },
        { status: 400 }
      );
    }

    // Validate pincode format (6 digits for India)
    if (!/^[0-9]{6}$/.test(pincode)) {
      return NextResponse.json(
        { error: 'Invalid pincode format' },
        { status: 400 }
      );
    }

    // Calculate total weight/value for shipping calculation
    const totalValue = cartItems.reduce((sum: number, item: any) => {
      const price = typeof item.price === 'string' ? parseFloat(item.price) : item.price;
      return sum + (price * item.quantity);
    }, 0);

    // Mock shipping rates based on pincode and order value
    // In a real implementation, this would integrate with shipping providers
    const shippingRates = [];

    // Standard shipping (always available)
    shippingRates.push({
      id: 'standard',
      name: 'Standard Shipping',
      cost: totalValue > 1000 ? 0 : 50, // Free shipping over ₹1000
      description: 'Delivered in 5-7 business days',
      estimatedDays: '5-7 days'
    });

    // Express shipping (available for most pincodes)
    if (!['110001', '400001', '560001'].includes(pincode)) {
      // Not available in some metro areas for this mock
      shippingRates.push({
        id: 'express',
        name: 'Express Shipping',
        cost: 150,
        description: 'Delivered in 2-3 business days',
        estimatedDays: '2-3 days'
      });
    }

    // Same day delivery (only for select pincodes)
    if (['110001', '400001', '560001', '600001'].includes(pincode)) {
      shippingRates.push({
        id: 'same_day',
        name: 'Same Day Delivery',
        cost: 300,
        description: 'Delivered today before 9 PM',
        estimatedDays: 'Today'
      });
    }

    return NextResponse.json(shippingRates);

  } catch (error) {
    console.error('Shipping rates error:', error);
    return NextResponse.json(
      { error: 'Failed to calculate shipping rates' },
      { status: 500 }
    );
  }
}
