"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["commons-src_lib_woocommerce_ts-ea0e4c9f"],{

/***/ "(app-pages-browser)/./src/lib/woocommerce.ts":
/*!********************************!*\
  !*** ./src/lib/woocommerce.ts ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ADD_TO_CART: function() { return /* binding */ ADD_TO_CART; },\n/* harmony export */   GET_CART: function() { return /* binding */ GET_CART; },\n/* harmony export */   MUTATION_LOGIN: function() { return /* binding */ MUTATION_LOGIN; },\n/* harmony export */   MUTATION_REMOVE_FROM_CART: function() { return /* binding */ MUTATION_REMOVE_FROM_CART; },\n/* harmony export */   QUERY_ALL_CATEGORIES: function() { return /* binding */ QUERY_ALL_CATEGORIES; },\n/* harmony export */   QUERY_ALL_PRODUCTS: function() { return /* binding */ QUERY_ALL_PRODUCTS; },\n/* harmony export */   QUERY_CATEGORY_PRODUCTS: function() { return /* binding */ QUERY_CATEGORY_PRODUCTS; },\n/* harmony export */   QUERY_GET_CART: function() { return /* binding */ QUERY_GET_CART; },\n/* harmony export */   QUERY_PAYMENT_GATEWAYS: function() { return /* binding */ QUERY_PAYMENT_GATEWAYS; },\n/* harmony export */   QUERY_SHIPPING_METHODS: function() { return /* binding */ QUERY_SHIPPING_METHODS; },\n/* harmony export */   addToCart: function() { return /* binding */ addToCart; },\n/* harmony export */   clearAuthToken: function() { return /* binding */ clearAuthToken; },\n/* harmony export */   createAddress: function() { return /* binding */ createAddress; },\n/* harmony export */   createCart: function() { return /* binding */ createCart; },\n/* harmony export */   createCustomer: function() { return /* binding */ createCustomer; },\n/* harmony export */   customerLogin: function() { return /* binding */ customerLogin; },\n/* harmony export */   deleteAddress: function() { return /* binding */ deleteAddress; },\n/* harmony export */   fetchFromWooCommerce: function() { return /* binding */ fetchFromWooCommerce; },\n/* harmony export */   formatPrice: function() { return /* binding */ formatPrice; },\n/* harmony export */   getAllCategories: function() { return /* binding */ getAllCategories; },\n/* harmony export */   getAllProducts: function() { return /* binding */ getAllProducts; },\n/* harmony export */   getCart: function() { return /* binding */ getCart; },\n/* harmony export */   getCategories: function() { return /* binding */ getCategories; },\n/* harmony export */   getCategoryProducts: function() { return /* binding */ getCategoryProducts; },\n/* harmony export */   getCategoryProductsWithTags: function() { return /* binding */ getCategoryProductsWithTags; },\n/* harmony export */   getCustomer: function() { return /* binding */ getCustomer; },\n/* harmony export */   getMetafield: function() { return /* binding */ getMetafield; },\n/* harmony export */   getProduct: function() { return /* binding */ getProduct; },\n/* harmony export */   getProductById: function() { return /* binding */ getProductById; },\n/* harmony export */   getProductBySlug: function() { return /* binding */ getProductBySlug; },\n/* harmony export */   getProductBySlugWithTags: function() { return /* binding */ getProductBySlugWithTags; },\n/* harmony export */   getProductVariations: function() { return /* binding */ getProductVariations; },\n/* harmony export */   getProducts: function() { return /* binding */ getProducts; },\n/* harmony export */   getSessionToken: function() { return /* binding */ getSessionToken; },\n/* harmony export */   getWooCommerceCheckoutUrl: function() { return /* binding */ getWooCommerceCheckoutUrl; },\n/* harmony export */   normalizeCart: function() { return /* binding */ normalizeCart; },\n/* harmony export */   normalizeCategory: function() { return /* binding */ normalizeCategory; },\n/* harmony export */   normalizeProduct: function() { return /* binding */ normalizeProduct; },\n/* harmony export */   normalizeProductImages: function() { return /* binding */ normalizeProductImages; },\n/* harmony export */   removeFromCart: function() { return /* binding */ removeFromCart; },\n/* harmony export */   searchProducts: function() { return /* binding */ searchProducts; },\n/* harmony export */   setAuthToken: function() { return /* binding */ setAuthToken; },\n/* harmony export */   setDefaultAddress: function() { return /* binding */ setDefaultAddress; },\n/* harmony export */   setSessionToken: function() { return /* binding */ setSessionToken; },\n/* harmony export */   updateAddress: function() { return /* binding */ updateAddress; },\n/* harmony export */   updateCart: function() { return /* binding */ updateCart; },\n/* harmony export */   updateCustomer: function() { return /* binding */ updateCustomer; },\n/* harmony export */   wooConfig: function() { return /* binding */ wooConfig; },\n/* harmony export */   wooGraphQLFetch: function() { return /* binding */ wooGraphQLFetch; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var graphql_request__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! graphql-request */ \"(app-pages-browser)/./node_modules/graphql-request/build/entrypoints/main.js\");\n/* harmony import */ var _wooInventoryMapping__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./wooInventoryMapping */ \"(app-pages-browser)/./src/lib/wooInventoryMapping.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// WooCommerce GraphQL API integration - Fixed according to official documentation\n\nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  fragment ProductFields on Product {\\n    id\\n    databaseId\\n    name\\n    slug\\n    description\\n    shortDescription\\n    type\\n    image {\\n      sourceUrl\\n      altText\\n    }\\n    galleryImages {\\n      nodes {\\n        sourceUrl\\n        altText\\n      }\\n    }\\n    ... on SimpleProduct {\\n      price\\n      regularPrice\\n      salePrice\\n      onSale\\n      stockStatus\\n      stockQuantity\\n    }\\n    ... on VariableProduct {\\n      price\\n      regularPrice\\n      salePrice\\n      onSale\\n      stockStatus\\n      stockQuantity\\n      attributes {\\n        nodes {\\n          name\\n          options\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  fragment VariableProductWithVariations on VariableProduct {\\n    attributes {\\n      nodes {\\n        name\\n        options\\n      }\\n    }\\n    variations {\\n      nodes {\\n        id\\n        databaseId\\n        name\\n        price\\n        regularPrice\\n        salePrice\\n        stockStatus\\n        stockQuantity\\n        attributes {\\n          nodes {\\n            name\\n            value\\n          }\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject2() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetProducts(\\n    $first: Int\\n    $after: String\\n    $where: RootQueryToProductConnectionWhereArgs\\n  ) {\\n    products(first: $first, after: $after, where: $where) {\\n      pageInfo {\\n        hasNextPage\\n        endCursor\\n      }\\n      nodes {\\n        ...ProductFields\\n        ... on VariableProduct {\\n          ...VariableProductWithVariations\\n        }\\n      }\\n    }\\n  }\\n  \",\n        \"\\n  \",\n        \"\\n\"\n    ]);\n    _templateObject2 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject3() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetProductBySlug($slug: ID!) {\\n    product(id: $slug, idType: SLUG) {\\n      ...ProductFields\\n      ... on VariableProduct {\\n        ...VariableProductWithVariations\\n      }\\n    }\\n  }\\n  \",\n        \"\\n  \",\n        \"\\n\"\n    ]);\n    _templateObject3 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject4() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetProductBySlugWithTags($slug: ID!) {\\n    product(id: $slug, idType: SLUG) {\\n      ...ProductFields\\n      ... on VariableProduct {\\n        ...VariableProductWithVariations\\n      }\\n      productTags {\\n        nodes {\\n          id\\n          name\\n          slug\\n        }\\n      }\\n      productCategories {\\n        nodes {\\n          id\\n          name\\n          slug\\n        }\\n      }\\n    }\\n  }\\n  \",\n        \"\\n  \",\n        \"\\n\"\n    ]);\n    _templateObject4 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject5() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetCategories(\\n    $first: Int\\n    $after: String\\n    $where: RootQueryToProductCategoryConnectionWhereArgs\\n  ) {\\n    productCategories(first: $first, after: $after, where: $where) {\\n      pageInfo {\\n        hasNextPage\\n        endCursor\\n      }\\n      nodes {\\n        id\\n        databaseId\\n        name\\n        slug\\n        description\\n        count\\n        image {\\n          sourceUrl\\n          altText\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject5 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject6() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n      query GetProductVariations($id: ID!) {\\n        product(id: $id, idType: DATABASE_ID) {\\n          ... on VariableProduct {\\n            variations {\\n              nodes {\\n                id\\n                databaseId\\n                name\\n                price\\n                regularPrice\\n                salePrice\\n                stockStatus\\n                attributes {\\n                  nodes {\\n                    name\\n                    value\\n                  }\\n                }\\n              }\\n            }\\n          }\\n        }\\n      }\\n    \"\n    ]);\n    _templateObject6 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject7() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetAllProducts($first: Int = 20) {\\n    products(first: $first) {\\n      nodes {\\n        id\\n        databaseId\\n        name\\n        slug\\n        description\\n        shortDescription\\n        productCategories {\\n          nodes {\\n            id\\n            name\\n            slug\\n          }\\n        }\\n        ... on SimpleProduct {\\n          price\\n          regularPrice\\n          salePrice\\n          onSale\\n          stockStatus\\n          stockQuantity\\n        }\\n        ... on VariableProduct {\\n          price\\n          regularPrice\\n          salePrice\\n          onSale\\n          stockStatus\\n          variations {\\n            nodes {\\n              stockStatus\\n              stockQuantity\\n            }\\n          }\\n        }\\n        image {\\n          id\\n          sourceUrl\\n          altText\\n        }\\n        galleryImages {\\n          nodes {\\n            id\\n            sourceUrl\\n            altText\\n          }\\n        }\\n        ... on VariableProduct {\\n          attributes {\\n            nodes {\\n              name\\n              options\\n            }\\n          }\\n        }\\n        ... on SimpleProduct {\\n          attributes {\\n            nodes {\\n              name\\n              options\\n            }\\n          }\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject7 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject8() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetProductsByCategory($slug: ID!, $first: Int = 20) {\\n    productCategory(id: $slug, idType: SLUG) {\\n      id\\n      name\\n      slug\\n      description\\n      products(first: $first) {\\n        nodes {\\n          id\\n          databaseId\\n          name\\n          slug\\n          ... on SimpleProduct {\\n            price\\n            regularPrice\\n            salePrice\\n            onSale\\n            stockStatus\\n          }\\n          ... on VariableProduct {\\n            price\\n            regularPrice\\n            salePrice\\n            onSale\\n            stockStatus\\n          }\\n          image {\\n            id\\n            sourceUrl\\n            altText\\n          }\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject8 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject9() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetAllCategories($first: Int = 20) {\\n    productCategories(first: $first) {\\n      nodes {\\n        id\\n        databaseId\\n        name\\n        slug\\n        description\\n        count\\n        image {\\n          sourceUrl\\n          altText\\n        }\\n        children {\\n          nodes {\\n            id\\n            name\\n            slug\\n          }\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject9 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject10() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetCart {\\n    cart {\\n      contents {\\n        nodes {\\n          key\\n          product {\\n            node {\\n              id\\n              databaseId\\n              name\\n              slug\\n              type\\n              image {\\n                sourceUrl\\n                altText\\n              }\\n            }\\n          }\\n          variation {\\n            node {\\n              id\\n              databaseId\\n              name\\n              attributes {\\n                nodes {\\n                  name\\n                  value\\n                }\\n              }\\n            }\\n          }\\n          quantity\\n          total\\n        }\\n      }\\n      subtotal\\n      total\\n      totalTax\\n      isEmpty\\n    }\\n  }\\n\"\n    ]);\n    _templateObject10 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject11() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        '\\n  mutation LoginUser($username: String!, $password: String!) {\\n    login(input: {\\n      clientMutationId: \"login\"\\n      username: $username\\n      password: $password\\n    }) {\\n      authToken\\n      refreshToken\\n      user {\\n        id\\n        databaseId\\n        email\\n        firstName\\n        lastName\\n        nicename\\n        nickname\\n        username\\n      }\\n    }\\n  }\\n'\n    ]);\n    _templateObject11 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject12() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetCart {\\n    cart {\\n      contents {\\n        nodes {\\n          key\\n          product {\\n            node {\\n              id\\n              databaseId\\n              name\\n              slug\\n              type\\n              image {\\n                sourceUrl\\n                altText\\n              }\\n            }\\n          }\\n          variation {\\n            node {\\n              id\\n              databaseId\\n              name\\n              attributes {\\n                nodes {\\n                  name\\n                  value\\n                }\\n              }\\n            }\\n          }\\n          quantity\\n          total\\n        }\\n      }\\n      subtotal\\n      total\\n      totalTax\\n      isEmpty\\n      contentsCount\\n    }\\n  }\\n\"\n    ]);\n    _templateObject12 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject13() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        '\\n  mutation AddToCart($productId: Int!, $variationId: Int, $quantity: Int, $extraData: String) {\\n    addToCart(\\n      input: {\\n        clientMutationId: \"addToCart\"\\n        productId: $productId\\n        variationId: $variationId\\n        quantity: $quantity\\n        extraData: $extraData\\n      }\\n    ) {\\n      cart {\\n        contents {\\n          nodes {\\n            key\\n            product {\\n              node {\\n                id\\n                databaseId\\n                name\\n                slug\\n                type\\n                image {\\n                  sourceUrl\\n                  altText\\n                }\\n              }\\n            }\\n            variation {\\n              node {\\n                id\\n                databaseId\\n                name\\n                attributes {\\n                  nodes {\\n                    name\\n                    value\\n                  }\\n                }\\n              }\\n            }\\n            quantity\\n            total\\n          }\\n        }\\n        subtotal\\n        total\\n        totalTax\\n        isEmpty\\n        contentsCount\\n      }\\n    }\\n  }\\n'\n    ]);\n    _templateObject13 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject14() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation RemoveItemsFromCart($keys: [ID]!, $all: Boolean) {\\n    removeItemsFromCart(input: { keys: $keys, all: $all }) {\\n      cart {\\n        contents {\\n          nodes {\\n            key\\n            product {\\n              node {\\n                id\\n                databaseId\\n                name\\n                slug\\n                type\\n                image {\\n                  sourceUrl\\n                  altText\\n                }\\n              }\\n            }\\n            variation {\\n              node {\\n                id\\n                databaseId\\n                name\\n                attributes {\\n                  nodes {\\n                    name\\n                    value\\n                  }\\n                }\\n              }\\n            }\\n            quantity\\n            total\\n          }\\n        }\\n        subtotal\\n        total\\n        totalTax\\n        isEmpty\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject14 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject15() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetShippingMethods {\\n    shippingMethods {\\n      nodes {\\n        id\\n        title\\n        description\\n        cost\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject15 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject16() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetPaymentGateways {\\n    paymentGateways {\\n      nodes {\\n        id\\n        title\\n        description\\n        enabled\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject16 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject17() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        '\\n      mutation LoginUser($username: String!, $password: String!) {\\n        login(input: {\\n          clientMutationId: \"login\"\\n          username: $username\\n          password: $password\\n        }) {\\n          authToken\\n          refreshToken\\n          user {\\n            id\\n            databaseId\\n            email\\n            firstName\\n            lastName\\n            nicename\\n            nickname\\n            username\\n          }\\n        }\\n      }\\n    '\n    ]);\n    _templateObject17 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject18() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n      mutation RegisterUser($input: RegisterCustomerInput!) {\\n        registerCustomer(input: $input) {\\n          clientMutationId\\n          authToken\\n          refreshToken\\n          customer {\\n            id\\n            databaseId\\n            email\\n            firstName\\n            lastName\\n          }\\n        }\\n      }\\n    \"\n    ]);\n    _templateObject18 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject19() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n      query GetCustomer {\\n        customer {\\n          id\\n          databaseId\\n          email\\n          firstName\\n          lastName\\n          displayName\\n          username\\n          role\\n          date\\n          modified\\n          isPayingCustomer\\n          orderCount\\n          totalSpent\\n          billing {\\n            firstName\\n            lastName\\n            company\\n            address1\\n            address2\\n            city\\n            state\\n            postcode\\n            country\\n            email\\n            phone\\n          }\\n          shipping {\\n            firstName\\n            lastName\\n            company\\n            address1\\n            address2\\n            city\\n            state\\n            postcode\\n            country\\n          }\\n          orders(first: 50) {\\n            nodes {\\n              id\\n              databaseId\\n              date\\n              status\\n              total\\n              subtotal\\n              totalTax\\n              shippingTotal\\n              discountTotal\\n              paymentMethodTitle\\n              customerNote\\n              billing {\\n                firstName\\n                lastName\\n                company\\n                address1\\n                address2\\n                city\\n                state\\n                postcode\\n                country\\n                email\\n                phone\\n              }\\n              shipping {\\n                firstName\\n                lastName\\n                company\\n                address1\\n                address2\\n                city\\n                state\\n                postcode\\n                country\\n              }\\n              lineItems {\\n                nodes {\\n                  product {\\n                    node {\\n                      id\\n                      name\\n                      slug\\n                      image {\\n                        sourceUrl\\n                        altText\\n                      }\\n                    }\\n                  }\\n                  variation {\\n                    node {\\n                      id\\n                      name\\n                      attributes {\\n                        nodes {\\n                          name\\n                          value\\n                        }\\n                      }\\n                    }\\n                  }\\n                  quantity\\n                  total\\n                  subtotal\\n                  totalTax\\n                }\\n              }\\n              shippingLines {\\n                nodes {\\n                  methodTitle\\n                  total\\n                }\\n              }\\n              feeLines {\\n                nodes {\\n                  name\\n                  total\\n                }\\n              }\\n              couponLines {\\n                nodes {\\n                  code\\n                  discount\\n                }\\n              }\\n            }\\n          }\\n          downloadableItems {\\n            nodes {\\n              name\\n              downloadId\\n              downloadsRemaining\\n              accessExpires\\n              product {\\n                node {\\n                  id\\n                  name\\n                }\\n              }\\n            }\\n          }\\n          metaData {\\n            key\\n            value\\n          }\\n        }\\n      }\\n    \"\n    ]);\n    _templateObject19 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject20() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n      query GetProductById($id: ID!) {\\n        product(id: $id, idType: DATABASE_ID) {\\n          id\\n          databaseId\\n          name\\n          slug\\n          description\\n          shortDescription\\n          productCategories {\\n            nodes {\\n              id\\n              name\\n              slug\\n            }\\n          }\\n          ... on SimpleProduct {\\n            price\\n            regularPrice\\n            salePrice\\n            onSale\\n            stockStatus\\n            stockQuantity\\n          }\\n          ... on VariableProduct {\\n            price\\n            regularPrice\\n            salePrice\\n            onSale\\n            stockStatus\\n            variations {\\n              nodes {\\n                stockStatus\\n                stockQuantity\\n              }\\n            }\\n          }\\n          image {\\n            id\\n            sourceUrl\\n            altText\\n          }\\n        }\\n      }\\n    \"\n    ]);\n    _templateObject20 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject21() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n    query SearchProducts($query: String!, $first: Int) {\\n      products(first: $first, where: { search: $query }) {\\n        nodes {\\n          id\\n          databaseId\\n          name\\n          slug\\n          price\\n          image {\\n            sourceUrl\\n            altText\\n          }\\n          shortDescription\\n        }\\n        pageInfo {\\n          hasNextPage\\n          endCursor\\n        }\\n      }\\n    }\\n  \"\n    ]);\n    _templateObject21 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject22() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n    query GetProduct($id: ID!) {\\n      product(id: $id, idType: DATABASE_ID) {\\n        id\\n        databaseId\\n        name\\n        slug\\n        description\\n        shortDescription\\n        price\\n        regularPrice\\n        salePrice\\n        onSale\\n        stockStatus\\n        stockQuantity\\n        image {\\n          sourceUrl\\n          altText\\n        }\\n        galleryImages {\\n          nodes {\\n            sourceUrl\\n            altText\\n          }\\n        }\\n        ... on SimpleProduct {\\n          attributes {\\n            nodes {\\n              name\\n              options\\n            }\\n          }\\n          price\\n          regularPrice\\n          salePrice\\n        }\\n        ... on VariableProduct {\\n          price\\n          regularPrice\\n          salePrice\\n          attributes {\\n            nodes {\\n              name\\n              options\\n            }\\n          }\\n          variations {\\n            nodes {\\n              id\\n              databaseId\\n              name\\n              price\\n              regularPrice\\n              salePrice\\n              stockStatus\\n              attributes {\\n                nodes {\\n                  name\\n                  value\\n                }\\n              }\\n            }\\n          }\\n        }\\n      }\\n    }\\n  \"\n    ]);\n    _templateObject22 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject23() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation CreateCustomer($input: RegisterCustomerInput!) {\\n    registerCustomer(input: $input) {\\n      customer {\\n        id\\n        databaseId\\n        email\\n        firstName\\n        lastName\\n        displayName\\n      }\\n      authToken\\n      refreshToken\\n    }\\n  }\\n\"\n    ]);\n    _templateObject23 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject24() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation UpdateCustomer($input: UpdateCustomerInput!) {\\n    updateCustomer(input: $input) {\\n      clientMutationId\\n      customer {\\n        id\\n        databaseId\\n        email\\n        firstName\\n        lastName\\n        displayName\\n        billing {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n          email\\n          phone\\n        }\\n        shipping {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n        }\\n      }\\n      customerUserErrors {\\n        field\\n        message\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject24 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject25() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetCustomer {\\n    customer {\\n      id\\n      databaseId\\n      email\\n      firstName\\n      lastName\\n      displayName\\n      username\\n      role\\n      date\\n      modified\\n      isPayingCustomer\\n      orderCount\\n      totalSpent\\n      billing {\\n        firstName\\n        lastName\\n        company\\n        address1\\n        address2\\n        city\\n        state\\n        postcode\\n        country\\n        email\\n        phone\\n      }\\n      shipping {\\n        firstName\\n        lastName\\n        company\\n        address1\\n        address2\\n        city\\n        state\\n        postcode\\n        country\\n      }\\n      orders(first: 50) {\\n        nodes {\\n          id\\n          databaseId\\n          date\\n          status\\n          total\\n          subtotal\\n          totalTax\\n          shippingTotal\\n          discountTotal\\n          paymentMethodTitle\\n          customerNote\\n          billing {\\n            firstName\\n            lastName\\n            company\\n            address1\\n            address2\\n            city\\n            state\\n            postcode\\n            country\\n            email\\n            phone\\n          }\\n          shipping {\\n            firstName\\n            lastName\\n            company\\n            address1\\n            address2\\n            city\\n            state\\n            postcode\\n            country\\n          }\\n          lineItems {\\n            nodes {\\n              product {\\n                node {\\n                  id\\n                  name\\n                  slug\\n                  image {\\n                    sourceUrl\\n                    altText\\n                  }\\n                }\\n              }\\n              variation {\\n                node {\\n                  id\\n                  name\\n                  attributes {\\n                    nodes {\\n                      name\\n                      value\\n                    }\\n                  }\\n                }\\n              }\\n              quantity\\n              total\\n              subtotal\\n              totalTax\\n            }\\n          }\\n          shippingLines {\\n            nodes {\\n              methodTitle\\n              total\\n            }\\n          }\\n          feeLines {\\n            nodes {\\n              name\\n              total\\n            }\\n          }\\n          couponLines {\\n            nodes {\\n              code\\n              discount\\n            }\\n          }\\n        }\\n      }\\n      downloadableItems {\\n        nodes {\\n          name\\n          downloadId\\n          downloadsRemaining\\n          accessExpires\\n          product {\\n            node {\\n              id\\n              name\\n            }\\n          }\\n        }\\n      }\\n      metaData {\\n        key\\n        value\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject25 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject26() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation CreateAddress($input: UpdateCustomerInput!) {\\n    updateCustomer(input: $input) {\\n      customer {\\n        id\\n        billing {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n          email\\n          phone\\n        }\\n        shipping {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject26 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject27() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation UpdateAddress($input: UpdateCustomerInput!) {\\n    updateCustomer(input: $input) {\\n      customer {\\n        id\\n        billing {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n          email\\n          phone\\n        }\\n        shipping {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject27 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject28() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation DeleteAddress($input: UpdateCustomerInput!) {\\n    updateCustomer(input: $input) {\\n      customer {\\n        id\\n        billing {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n          email\\n          phone\\n        }\\n        shipping {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject28 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject29() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation SetDefaultAddress($input: UpdateCustomerInput!) {\\n    updateCustomer(input: $input) {\\n      customer {\\n        id\\n        billing {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n          email\\n          phone\\n        }\\n        shipping {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject29 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject30() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation UpdateCart($input: UpdateItemQuantitiesInput!) {\\n    updateItemQuantities(input: $input) {\\n      cart {\\n        contents {\\n          nodes {\\n            key\\n            product {\\n              node {\\n                id\\n                name\\n                price\\n              }\\n            }\\n            quantity\\n            total\\n          }\\n        }\\n        subtotal\\n        total\\n        totalTax\\n        isEmpty\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject30 = function() {\n        return data;\n    };\n    return data;\n}\n\n\n\n// WooCommerce store configuration\nconst wooConfig = {\n    storeUrl: \"https://maroon-lapwing-781450.hostingersite.com\" || 0,\n    graphqlUrl: process.env.WOOCOMMERCE_GRAPHQL_URL || \"https://your-wordpress-site.com/graphql\",\n    apiVersion: \"v1\"\n};\n// Session management for WooCommerce\nlet sessionToken = null;\nconst getSessionToken = ()=>{\n    if (true) {\n        return sessionStorage.getItem(\"woo-session-token\") || sessionToken;\n    }\n    return sessionToken;\n};\nconst setSessionToken = (token)=>{\n    sessionToken = token;\n    if (true) {\n        if (token) {\n            sessionStorage.setItem(\"woo-session-token\", token);\n        } else {\n            sessionStorage.removeItem(\"woo-session-token\");\n        }\n    }\n};\n// Check if code is running on client or server\nconst isClient = \"object\" !== \"undefined\";\n// Initialize GraphQL client with proper headers for CORS\nconst endpoint = process.env.WOOCOMMERCE_GRAPHQL_URL || \"https://your-wordpress-site.com/graphql\";\nconst graphQLClient = new graphql_request__WEBPACK_IMPORTED_MODULE_1__.GraphQLClient(endpoint, {\n    headers: {\n        \"Content-Type\": \"application/json\",\n        \"Accept\": \"application/json\"\n    }\n});\n// Set auth token for authenticated requests\nconst setAuthToken = (token)=>{\n    graphQLClient.setHeader(\"Authorization\", \"Bearer \".concat(token));\n};\n// Clear auth token for unauthenticated requests\nconst clearAuthToken = ()=>{\n    graphQLClient.setHeaders({\n        \"Content-Type\": \"application/json\",\n        \"Accept\": \"application/json\"\n    });\n};\n// GraphQL fragments\nconst PRODUCT_FRAGMENT = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject());\n// Define a separate fragment for variable products with variations\nconst VARIABLE_PRODUCT_FRAGMENT = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject1());\n// Queries\nconst GET_PRODUCTS = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject2(), PRODUCT_FRAGMENT, VARIABLE_PRODUCT_FRAGMENT);\nconst GET_PRODUCT_BY_SLUG = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject3(), PRODUCT_FRAGMENT, VARIABLE_PRODUCT_FRAGMENT);\nconst GET_PRODUCT_BY_SLUG_WITH_TAGS = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject4(), PRODUCT_FRAGMENT, VARIABLE_PRODUCT_FRAGMENT);\nconst GET_CATEGORIES = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject5());\n// Fetch functions\nasync function getProducts() {\n    let variables = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    try {\n        const data = await fetchFromWooCommerce(GET_PRODUCTS, {\n            first: variables.first || 12,\n            after: variables.after || null,\n            where: variables.where || {}\n        }, [\n            \"products\"\n        ], 60);\n        return data.products;\n    } catch (error) {\n        console.error(\"Error fetching products:\", error);\n        return {\n            nodes: [],\n            pageInfo: {\n                hasNextPage: false,\n                endCursor: null\n            }\n        };\n    }\n}\n/**\n * Get variations for a variable product\n */ async function getProductVariations(productId) {\n    try {\n        var _response_product_variations, _response_product;\n        const query = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject6());\n        const response = await fetchFromWooCommerce(query, {\n            id: productId\n        }, [\n            \"product-\".concat(productId),\n            \"products\"\n        ], 60);\n        return ((_response_product = response.product) === null || _response_product === void 0 ? void 0 : (_response_product_variations = _response_product.variations) === null || _response_product_variations === void 0 ? void 0 : _response_product_variations.nodes) || [];\n    } catch (error) {\n        console.error(\"Error fetching product variations:\", error);\n        return [];\n    }\n}\n/**\n * Get a product by its slug\n */ async function getProductBySlug(slug) {\n    try {\n        const data = await fetchFromWooCommerce(GET_PRODUCT_BY_SLUG, {\n            slug\n        }, [\n            \"product-\".concat(slug),\n            \"products\"\n        ], 60);\n        const product = data.product;\n        // If it's a variable product, fetch variations separately\n        if (product && product.type === \"VARIABLE\") {\n            const variations = await getProductVariations(product.databaseId);\n            return {\n                ...product,\n                variations: {\n                    nodes: variations\n                }\n            };\n        }\n        return product;\n    } catch (error) {\n        console.error(\"Error fetching product by slug:\", error);\n        return null;\n    }\n}\nasync function getProductBySlugWithTags(slug) {\n    try {\n        const data = await fetchFromWooCommerce(GET_PRODUCT_BY_SLUG_WITH_TAGS, {\n            slug\n        }, [\n            \"product-\".concat(slug),\n            \"products\"\n        ], 60);\n        return data.product;\n    } catch (error) {\n        console.error(\"Error fetching product with slug \".concat(slug, \":\"), error);\n        return null;\n    }\n}\n// Categories functionality is now handled by the more comprehensive getAllCategories function\n// Helper function to format price\nfunction formatPrice(price) {\n    const numericPrice = typeof price === \"string\" ? parseFloat(price) : price;\n    return numericPrice.toFixed(2);\n}\n/**\n * Fetch data from WooCommerce GraphQL API with caching and revalidation\n */ async function fetchFromWooCommerce(query) {\n    let variables = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, tags = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [], revalidate = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 60;\n    try {\n        // Use different approaches for client and server\n        if (isClient) {\n            // When on client, use our proxy API route to avoid CORS issues\n            const proxyEndpoint = \"/api/graphql\";\n            // Build the fetch options with session token\n            const headers = {\n                \"Content-Type\": \"application/json\"\n            };\n            // Add session token if available\n            const sessionToken = getSessionToken();\n            if (sessionToken) {\n                headers[\"woocommerce-session\"] = \"Session \".concat(sessionToken);\n            }\n            const fetchOptions = {\n                method: \"POST\",\n                headers,\n                body: JSON.stringify({\n                    query,\n                    variables\n                })\n            };\n            // Make the fetch request through our proxy\n            const response = await fetch(proxyEndpoint, fetchOptions);\n            if (!response.ok) {\n                throw new Error(\"GraphQL API responded with status \".concat(response.status));\n            }\n            // Extract session token from response headers if available\n            const responseSessionHeader = response.headers.get(\"woocommerce-session\");\n            if (responseSessionHeader) {\n                const token = responseSessionHeader.replace(\"Session \", \"\");\n                setSessionToken(token);\n            }\n            const { data, errors } = await response.json();\n            if (errors) {\n                console.error(\"GraphQL Errors:\", errors);\n                throw new Error(errors[0].message);\n            }\n            return data;\n        } else {\n            // Server-side code can directly access the WooCommerce GraphQL endpoint\n            // Build the fetch options with cache control\n            const fetchOptions = {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    query,\n                    variables\n                }),\n                next: {}\n            };\n            // Add cache tags if provided\n            if (tags && tags.length > 0) {\n                fetchOptions.next.tags = tags;\n            }\n            // Add revalidation if provided\n            if (revalidate !== undefined) {\n                fetchOptions.next.revalidate = revalidate;\n            }\n            // Make the fetch request\n            const response = await fetch(wooConfig.graphqlUrl, fetchOptions);\n            if (!response.ok) {\n                throw new Error(\"WooCommerce GraphQL API responded with status \".concat(response.status));\n            }\n            const { data, errors } = await response.json();\n            if (errors) {\n                console.error(\"GraphQL Errors:\", errors);\n                throw new Error(errors[0].message);\n            }\n            return data;\n        }\n    } catch (error) {\n        console.error(\"Error fetching from WooCommerce:\", error);\n        throw error;\n    }\n}\n/**\n * Base implementation of WooCommerce fetch that can be used by other modules\n * This provides a standardized way to make WooGraphQL API requests with retry logic\n * \n * @param query GraphQL query to execute \n * @param variables Variables for the GraphQL query\n * @param retries Number of retries in case of failure\n * @param delay Delay between retries in milliseconds\n * @returns The fetched data\n */ async function wooGraphQLFetch(param) {\n    let { query, variables } = param, retries = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3, delay = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1000;\n    let attemptCount = 0;\n    let lastError = null;\n    while(attemptCount < retries){\n        try {\n            // Use fetchFromWooCommerce for the actual request, but ignore caching controls\n            // as this is the low-level function that might be used in different contexts\n            const data = await fetchFromWooCommerce(query, variables, [], 0);\n            return data;\n        } catch (error) {\n            lastError = error;\n            attemptCount++;\n            if (attemptCount < retries) {\n                console.log(\"Retrying request (\".concat(attemptCount, \"/\").concat(retries, \") after \").concat(delay, \"ms\"));\n                await new Promise((resolve)=>setTimeout(resolve, delay));\n                // Exponential backoff\n                delay *= 2;\n            }\n        }\n    }\n    console.error(\"Failed after \".concat(retries, \" attempts:\"), lastError);\n    throw lastError;\n}\n/**\n * Get products by category with cache tags for efficient revalidation\n * \n * @param slug The category slug\n * @param first Number of products to fetch\n * @param revalidate Revalidation period in seconds (optional)\n * @returns The category with products\n */ async function getCategoryProductsWithTags(slug) {\n    let first = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20, revalidate = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 60;\n    try {\n        // Define cache tags for this category\n        const tags = [\n            \"category-\".concat(slug),\n            \"categories\",\n            \"products\"\n        ];\n        // Fetch the category with tags\n        const data = await fetchFromWooCommerce(QUERY_CATEGORY_PRODUCTS, {\n            slug,\n            first\n        }, tags, revalidate);\n        return (data === null || data === void 0 ? void 0 : data.productCategory) || null;\n    } catch (error) {\n        console.error(\"Error fetching category with slug \".concat(slug, \":\"), error);\n        throw error;\n    }\n}\n// GraphQL query to fetch all products\nconst QUERY_ALL_PRODUCTS = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject7());\n// GraphQL query to fetch products by category\nconst QUERY_CATEGORY_PRODUCTS = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject8());\n// GraphQL query to fetch all categories\nconst QUERY_ALL_CATEGORIES = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject9());\n// GraphQL query to get cart contents - Updated for current WooGraphQL schema\nconst QUERY_GET_CART = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject10());\n// Mutation for customer login\nconst MUTATION_LOGIN = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject11());\n// Get cart query - WooCommerce automatically creates a cart when needed\nconst GET_CART = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject12());\n// Add to cart mutation - Updated for current WooGraphQL schema\nconst ADD_TO_CART = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject13());\n// Remove from cart mutation - Updated for current WooGraphQL schema\nconst MUTATION_REMOVE_FROM_CART = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject14());\n// Shipping and payment related queries\nconst QUERY_SHIPPING_METHODS = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject15());\nconst QUERY_PAYMENT_GATEWAYS = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject16());\n// Implement core API methods\n/**\n * Get all products with pagination\n */ async function getAllProducts() {\n    let first = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20;\n    try {\n        var _data_products;\n        const data = await wooGraphQLFetch({\n            query: QUERY_ALL_PRODUCTS,\n            variables: {\n                first\n            }\n        });\n        return (data === null || data === void 0 ? void 0 : (_data_products = data.products) === null || _data_products === void 0 ? void 0 : _data_products.nodes) || [];\n    } catch (error) {\n        console.error(\"Error fetching all products:\", error);\n        return [];\n    }\n}\n/**\n * Get all categories with pagination\n */ async function getAllCategories() {\n    let first = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20;\n    try {\n        var _data_productCategories;\n        const data = await wooGraphQLFetch({\n            query: QUERY_ALL_CATEGORIES,\n            variables: {\n                first\n            }\n        });\n        return (data === null || data === void 0 ? void 0 : (_data_productCategories = data.productCategories) === null || _data_productCategories === void 0 ? void 0 : _data_productCategories.nodes) || [];\n    } catch (error) {\n        console.error(\"Error fetching all categories:\", error);\n        return [];\n    }\n}\n/**\n * Get product categories with pagination and filtering\n * @param variables Object containing:\n *   - first: Number of categories to return (default: 20)\n *   - after: Cursor for pagination\n *   - where: Filter criteria (parent, search, etc.)\n * @returns Object containing categories and pagination info\n */ async function getCategories() {\n    let variables = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    try {\n        const result = await wooGraphQLFetch({\n            query: QUERY_ALL_CATEGORIES,\n            variables: {\n                first: variables.first || 20,\n                after: variables.after || null,\n                where: variables.where || {}\n            }\n        });\n        return {\n            nodes: result.productCategories.nodes,\n            pageInfo: result.productCategories.pageInfo\n        };\n    } catch (error) {\n        console.error(\"Error fetching categories:\", error);\n        return {\n            nodes: [],\n            pageInfo: {\n                hasNextPage: false,\n                endCursor: null\n            }\n        };\n    }\n}\n/**\n * Create a new cart by adding the first item - WooCommerce automatically creates cart\n */ async function createCart() {\n    let items = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];\n    try {\n        if (items.length === 0) {\n            // Just return an empty cart structure - WooCommerce will create cart when first item is added\n            return {\n                contents: {\n                    nodes: []\n                },\n                subtotal: \"0\",\n                total: \"0\",\n                totalTax: \"0\",\n                isEmpty: true,\n                contentsCount: 0\n            };\n        }\n        // Add the first item to create the cart\n        const firstItem = items[0];\n        const cart = await addToCart(\"\", [\n            firstItem\n        ]);\n        // Add remaining items if any\n        if (items.length > 1) {\n            for(let i = 1; i < items.length; i++){\n                await addToCart(\"\", [\n                    items[i]\n                ]);\n            }\n            // Get the updated cart\n            return await getCart();\n        }\n        return cart;\n    } catch (error) {\n        console.error(\"Error creating cart:\", error);\n        throw error;\n    }\n}\n/**\n * Get cart contents - Updated for current WooGraphQL schema\n */ async function getCart() {\n    try {\n        const data = await wooGraphQLFetch({\n            query: GET_CART,\n            variables: {} // Cart query doesn't need parameters in current WooGraphQL\n        });\n        return (data === null || data === void 0 ? void 0 : data.cart) || null;\n    } catch (error) {\n        console.error(\"Error fetching cart:\", error);\n        return null;\n    }\n}\n/**\n * Add items to cart - Updated for current WooGraphQL schema\n */ async function addToCart(_cartId, items) {\n    try {\n        // WooCommerce GraphQL addToCart only accepts one item at a time\n        // So we'll add the first item and return the cart\n        if (items.length === 0) {\n            throw new Error(\"No items provided to add to cart\");\n        }\n        const item = items[0];\n        const variables = {\n            productId: parseInt(item.productId),\n            quantity: item.quantity || 1,\n            variationId: item.variationId ? parseInt(item.variationId) : null,\n            extraData: null\n        };\n        console.log(\"Adding to cart with variables:\", variables);\n        const response = await wooGraphQLFetch({\n            query: ADD_TO_CART,\n            variables\n        });\n        console.log(\"Add to cart response:\", response);\n        return response.addToCart.cart;\n    } catch (error) {\n        console.error(\"Error adding items to cart:\", error);\n        throw error;\n    }\n}\n/**\n * Remove items from cart - Updated for current WooGraphQL schema\n */ async function removeFromCart(cartId, keys) {\n    try {\n        var _data_removeItemsFromCart;\n        const data = await wooGraphQLFetch({\n            query: MUTATION_REMOVE_FROM_CART,\n            variables: {\n                keys,\n                all: false\n            }\n        });\n        return (data === null || data === void 0 ? void 0 : (_data_removeItemsFromCart = data.removeItemsFromCart) === null || _data_removeItemsFromCart === void 0 ? void 0 : _data_removeItemsFromCart.cart) || null;\n    } catch (error) {\n        console.error(\"Error removing items from cart:\", error);\n        throw error;\n    }\n}\n/**\n * Customer login with WooCommerce GraphQL\n * \n * @param username User's email/username\n * @param password User's password\n * @returns Authentication token and user information\n */ async function customerLogin(username, password) {\n    try {\n        const LOGIN_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject17());\n        const variables = {\n            username,\n            password\n        };\n        const result = await wooGraphQLFetch({\n            query: LOGIN_MUTATION,\n            variables\n        });\n        if (!result || !result.login || !result.login.authToken) {\n            throw new Error(\"Login failed: Invalid response from server\");\n        }\n        // Set the auth token for future requests\n        setAuthToken(result.login.authToken);\n        return {\n            authToken: result.login.authToken,\n            refreshToken: result.login.refreshToken,\n            user: result.login.user,\n            customerUserErrors: [] // For compatibility with Shopify auth\n        };\n    } catch (error) {\n        console.error(\"Login error:\", error);\n        // Format the error to match the expected structure\n        return {\n            authToken: null,\n            refreshToken: null,\n            user: null,\n            customerUserErrors: [\n                {\n                    code: \"LOGIN_FAILED\",\n                    message: error.message || \"Login failed. Please check your credentials.\"\n                }\n            ]\n        };\n    }\n}\n/**\n * Create customer (register) with WooCommerce GraphQL\n */ async function createCustomer(param) {\n    let { email, password, firstName, lastName, phone, acceptsMarketing = false } = param;\n    try {\n        const REGISTER_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject18());\n        const variables = {\n            input: {\n                clientMutationId: \"registerCustomer\",\n                email,\n                password,\n                firstName,\n                lastName,\n                username: email\n            }\n        };\n        const result = await wooGraphQLFetch({\n            query: REGISTER_MUTATION,\n            variables\n        });\n        if (!result || !result.registerCustomer) {\n            throw new Error(\"Registration failed: Invalid response from server\");\n        }\n        return {\n            customer: result.registerCustomer.customer,\n            authToken: result.registerCustomer.authToken,\n            refreshToken: result.registerCustomer.refreshToken,\n            customerUserErrors: [] // For compatibility with Shopify auth\n        };\n    } catch (error) {\n        console.error(\"Registration error:\", error);\n        // Format the error to match the expected structure\n        return {\n            customer: null,\n            authToken: null,\n            refreshToken: null,\n            customerUserErrors: [\n                {\n                    code: \"REGISTRATION_FAILED\",\n                    message: error.message || \"Registration failed. Please try again.\"\n                }\n            ]\n        };\n    }\n}\n/**\n * Get customer data using JWT authentication\n * \n * @param token JWT auth token\n * @returns Customer data\n */ async function getCustomer(token) {\n    try {\n        if (token) {\n            setAuthToken(token);\n        }\n        const GET_CUSTOMER_QUERY = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject19());\n        const result = await wooGraphQLFetch({\n            query: GET_CUSTOMER_QUERY\n        });\n        if (!result || !result.customer) {\n            throw new Error(\"Failed to get customer data\");\n        }\n        return result.customer;\n    } catch (error) {\n        console.error(\"Error getting customer data:\", error);\n        throw error;\n    } finally{\n        if (token) {\n            clearAuthToken();\n        }\n    }\n}\n/**\n * Normalize product data to match the existing frontend structure\n * This helps maintain compatibility with the existing components\n */ function normalizeProduct(product) {\n    var _product_variations_nodes, _product_variations, _product_variations_nodes1, _product_variations1, _product_variations_nodes2, _product_variations2, _product_attributes_nodes, _product_attributes, _product_productCategories_nodes, _product_productCategories;\n    if (!product) return null;\n    // Extract product type\n    const isVariable = Boolean((_product_variations = product.variations) === null || _product_variations === void 0 ? void 0 : (_product_variations_nodes = _product_variations.nodes) === null || _product_variations_nodes === void 0 ? void 0 : _product_variations_nodes.length);\n    // Extract pricing data\n    let priceRange = {\n        minVariantPrice: {\n            amount: product.price || \"0\",\n            currencyCode: \"INR\" // Default currency for the application\n        },\n        maxVariantPrice: {\n            amount: product.price || \"0\",\n            currencyCode: \"INR\"\n        }\n    };\n    // For variable products, calculate actual price range\n    if (isVariable && ((_product_variations1 = product.variations) === null || _product_variations1 === void 0 ? void 0 : (_product_variations_nodes1 = _product_variations1.nodes) === null || _product_variations_nodes1 === void 0 ? void 0 : _product_variations_nodes1.length) > 0) {\n        const prices = product.variations.nodes.map((variant)=>parseFloat(variant.price || \"0\")).filter((price)=>!isNaN(price));\n        if (prices.length > 0) {\n            priceRange = {\n                minVariantPrice: {\n                    amount: String(Math.min(...prices)),\n                    currencyCode: \"INR\"\n                },\n                maxVariantPrice: {\n                    amount: String(Math.max(...prices)),\n                    currencyCode: \"INR\"\n                }\n            };\n        }\n    }\n    // Extract and normalize images\n    const images = normalizeProductImages(product);\n    // Extract variant data\n    const variants = ((_product_variations2 = product.variations) === null || _product_variations2 === void 0 ? void 0 : (_product_variations_nodes2 = _product_variations2.nodes) === null || _product_variations_nodes2 === void 0 ? void 0 : _product_variations_nodes2.map((variant)=>{\n        var _variant_attributes_nodes, _variant_attributes;\n        return {\n            id: variant.id,\n            title: variant.name,\n            price: {\n                amount: variant.price || \"0\",\n                currencyCode: \"USD\"\n            },\n            availableForSale: variant.stockStatus === \"IN_STOCK\",\n            selectedOptions: ((_variant_attributes = variant.attributes) === null || _variant_attributes === void 0 ? void 0 : (_variant_attributes_nodes = _variant_attributes.nodes) === null || _variant_attributes_nodes === void 0 ? void 0 : _variant_attributes_nodes.map((attr)=>({\n                    name: attr.name,\n                    value: attr.value\n                }))) || [],\n            sku: variant.sku || \"\",\n            image: variant.image ? {\n                url: variant.image.sourceUrl,\n                altText: variant.image.altText || \"\"\n            } : null\n        };\n    })) || [];\n    // Extract options data for variable products\n    const options = ((_product_attributes = product.attributes) === null || _product_attributes === void 0 ? void 0 : (_product_attributes_nodes = _product_attributes.nodes) === null || _product_attributes_nodes === void 0 ? void 0 : _product_attributes_nodes.map((attr)=>({\n            name: attr.name,\n            values: attr.options || []\n        }))) || [];\n    // Extract category data\n    const collections = ((_product_productCategories = product.productCategories) === null || _product_productCategories === void 0 ? void 0 : (_product_productCategories_nodes = _product_productCategories.nodes) === null || _product_productCategories_nodes === void 0 ? void 0 : _product_productCategories_nodes.map((category)=>({\n            handle: category.slug,\n            title: category.name\n        }))) || [];\n    // Extract meta fields for custom data\n    const metafields = {};\n    if (product.metafields) {\n        product.metafields.forEach((meta)=>{\n            metafields[meta.key] = meta.value;\n        });\n    }\n    // Return normalized product object that matches existing frontend structure\n    return {\n        id: product.id,\n        handle: product.slug,\n        title: product.name,\n        description: product.description || \"\",\n        descriptionHtml: product.description || \"\",\n        priceRange,\n        options,\n        variants,\n        images,\n        collections,\n        availableForSale: product.stockStatus !== \"OUT_OF_STOCK\",\n        metafields,\n        // Add original data for reference if needed\n        _originalWooProduct: product\n    };\n}\n/**\n * Normalize product images array\n */ function normalizeProductImages(product) {\n    var _product_galleryImages_nodes, _product_galleryImages;\n    const images = [];\n    // Add main product image if available\n    if (product.image) {\n        images.push({\n            url: product.image.sourceUrl,\n            altText: product.image.altText || product.name || \"\"\n        });\n    }\n    // Add gallery images if available\n    if ((_product_galleryImages = product.galleryImages) === null || _product_galleryImages === void 0 ? void 0 : (_product_galleryImages_nodes = _product_galleryImages.nodes) === null || _product_galleryImages_nodes === void 0 ? void 0 : _product_galleryImages_nodes.length) {\n        product.galleryImages.nodes.forEach((img)=>{\n            // Avoid duplicating the main image if it's already in the gallery\n            const isMainImage = product.image && img.sourceUrl === product.image.sourceUrl;\n            if (!isMainImage) {\n                images.push({\n                    url: img.sourceUrl,\n                    altText: img.altText || product.name || \"\"\n                });\n            }\n        });\n    }\n    return images;\n}\n/**\n * Normalize category data to match existing frontend structure\n */ function normalizeCategory(category) {\n    var _category_products_nodes, _category_products;\n    if (!category) return null;\n    return {\n        id: category.id,\n        handle: category.slug,\n        title: category.name,\n        description: category.description || \"\",\n        image: category.image ? {\n            url: category.image.sourceUrl,\n            altText: category.image.altText || \"\"\n        } : null,\n        products: ((_category_products = category.products) === null || _category_products === void 0 ? void 0 : (_category_products_nodes = _category_products.nodes) === null || _category_products_nodes === void 0 ? void 0 : _category_products_nodes.map(normalizeProduct)) || []\n    };\n}\n/**\n * Get custom meta field from product\n */ const getMetafield = function(product, key, namespace) {\n    let defaultValue = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : \"\";\n    if (!product || !product.metafields) return defaultValue;\n    // Find the meta field by key\n    if (namespace) {\n        const metaKey = \"\".concat(namespace, \":\").concat(key);\n        return product.metafields[metaKey] || defaultValue;\n    }\n    return product.metafields[key] || defaultValue;\n};\n/**\n * Normalize cart data to match existing frontend structure\n */ function normalizeCart(cart) {\n    var _cart_contents_nodes, _cart_contents, _cart_appliedCoupons_nodes, _cart_appliedCoupons;\n    if (!cart) return null;\n    const lineItems = ((_cart_contents = cart.contents) === null || _cart_contents === void 0 ? void 0 : (_cart_contents_nodes = _cart_contents.nodes) === null || _cart_contents_nodes === void 0 ? void 0 : _cart_contents_nodes.map((item)=>{\n        var _item_product, _item_variation, _variation_attributes_nodes, _variation_attributes;\n        const product = (_item_product = item.product) === null || _item_product === void 0 ? void 0 : _item_product.node;\n        const variation = (_item_variation = item.variation) === null || _item_variation === void 0 ? void 0 : _item_variation.node;\n        return {\n            id: item.key,\n            quantity: item.quantity,\n            merchandise: {\n                id: (variation === null || variation === void 0 ? void 0 : variation.id) || (product === null || product === void 0 ? void 0 : product.id),\n                title: (variation === null || variation === void 0 ? void 0 : variation.name) || (product === null || product === void 0 ? void 0 : product.name),\n                product: {\n                    id: product === null || product === void 0 ? void 0 : product.id,\n                    handle: product === null || product === void 0 ? void 0 : product.slug,\n                    title: product === null || product === void 0 ? void 0 : product.name,\n                    image: (product === null || product === void 0 ? void 0 : product.image) ? {\n                        url: product === null || product === void 0 ? void 0 : product.image.sourceUrl,\n                        altText: (product === null || product === void 0 ? void 0 : product.image.altText) || \"\"\n                    } : null\n                },\n                selectedOptions: (variation === null || variation === void 0 ? void 0 : (_variation_attributes = variation.attributes) === null || _variation_attributes === void 0 ? void 0 : (_variation_attributes_nodes = _variation_attributes.nodes) === null || _variation_attributes_nodes === void 0 ? void 0 : _variation_attributes_nodes.map((attr)=>({\n                        name: attr.name,\n                        value: attr.value\n                    }))) || []\n            },\n            cost: {\n                totalAmount: {\n                    amount: item.total || \"0\",\n                    currencyCode: \"USD\"\n                }\n            }\n        };\n    })) || [];\n    const discountCodes = ((_cart_appliedCoupons = cart.appliedCoupons) === null || _cart_appliedCoupons === void 0 ? void 0 : (_cart_appliedCoupons_nodes = _cart_appliedCoupons.nodes) === null || _cart_appliedCoupons_nodes === void 0 ? void 0 : _cart_appliedCoupons_nodes.map((coupon)=>({\n            code: coupon.code,\n            amount: coupon.discountAmount || \"0\"\n        }))) || [];\n    // Calculate total quantity from line items instead of using contentsCount\n    const totalQuantity = lineItems.reduce((sum, item)=>sum + item.quantity, 0);\n    return {\n        id: cart.id,\n        checkoutUrl: \"\",\n        totalQuantity: totalQuantity,\n        cost: {\n            subtotalAmount: {\n                amount: cart.subtotal || \"0\",\n                currencyCode: \"USD\"\n            },\n            totalAmount: {\n                amount: cart.total || \"0\",\n                currencyCode: \"USD\"\n            }\n        },\n        lines: lineItems,\n        discountCodes\n    };\n}\n/**\n * Generates a checkout URL for WooCommerce\n * \n * @param cartId The cart ID to associate with checkout\n * @param isLoggedIn Whether the user is logged in\n * @returns The WooCommerce checkout URL\n */ function getWooCommerceCheckoutUrl(cartId) {\n    let isLoggedIn = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n    // Base checkout URL\n    const baseUrl = \"\".concat(wooConfig.storeUrl, \"/checkout\");\n    // Add cart parameter if needed\n    const cartParam = cartId ? \"?cart=\".concat(cartId) : \"\";\n    // Add comprehensive guest checkout parameters to ensure login is bypassed\n    // These parameters will work across different WooCommerce configurations and plugins\n    let guestParams = \"\";\n    if (!isLoggedIn) {\n        const separator = cartParam ? \"&\" : \"?\";\n        guestParams = \"\".concat(separator, \"guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1\");\n    }\n    // Construct the full URL\n    return \"\".concat(baseUrl).concat(cartParam).concat(guestParams);\n}\n/**\n * Get a product by its ID\n * @param id The product ID\n * @param revalidate Revalidation time in seconds\n * @returns The product data or a fallback product if not found\n */ async function getProductById(id) {\n    let revalidate = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 60;\n    try {\n        // Check if ID is in a valid format\n        if (!id || id === \"undefined\" || id === \"null\") {\n            console.warn(\"Invalid product ID format: \".concat(id, \", returning fallback product\"));\n            return createFallbackProduct(id);\n        }\n        // Validate and transform the product ID\n        const validatedId = await (0,_wooInventoryMapping__WEBPACK_IMPORTED_MODULE_2__.validateProductId)(id);\n        // Define cache tags for this product\n        const tags = [\n            \"product-\".concat(validatedId),\n            \"products\",\n            \"inventory\"\n        ];\n        // Define the query\n        const QUERY_PRODUCT_BY_ID = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject20());\n        try {\n            // Fetch the product with tags\n            const data = await fetchFromWooCommerce(QUERY_PRODUCT_BY_ID, {\n                id: validatedId\n            }, tags, revalidate);\n            // Check if product exists\n            if (!(data === null || data === void 0 ? void 0 : data.product)) {\n                console.warn(\"No product found with ID: \".concat(id, \", returning fallback product\"));\n                return createFallbackProduct(id);\n            }\n            return data.product;\n        } catch (error) {\n            console.error(\"Error fetching product with ID \".concat(id, \":\"), error);\n            // Return a fallback product instead of throwing an error\n            return createFallbackProduct(id);\n        }\n    } catch (error) {\n        console.error(\"Error in getProductById for ID \".concat(id, \":\"), error);\n        // Return a fallback product instead of throwing an error\n        return createFallbackProduct(id);\n    }\n}\n/**\n * Create a fallback product for when a product cannot be found\n * @param id The original product ID\n * @returns A fallback product object\n */ function createFallbackProduct(id) {\n    return {\n        id: id,\n        databaseId: 0,\n        name: \"Product Not Found\",\n        slug: \"product-not-found\",\n        description: \"This product is no longer available.\",\n        shortDescription: \"Product not found\",\n        price: \"0.00\",\n        regularPrice: \"0.00\",\n        salePrice: null,\n        onSale: false,\n        stockStatus: \"OUT_OF_STOCK\",\n        stockQuantity: 0,\n        image: {\n            id: null,\n            sourceUrl: \"/placeholder-product.jpg\",\n            altText: \"Product not found\"\n        },\n        productCategories: {\n            nodes: []\n        }\n    };\n}\n/**\n * Search products by keyword with advanced options\n * @param query Search query\n * @param options Search options including pagination, sorting, filtering\n * @returns Products matching the search query\n */ async function searchProducts(query) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    // Handle case where options is passed as a number for backward compatibility\n    const first = typeof options === \"number\" ? options : options.first || 10;\n    const searchQuery = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject21());\n    try {\n        const data = await graphQLClient.request(searchQuery, {\n            query,\n            first\n        });\n        return (data === null || data === void 0 ? void 0 : data.products) || {\n            nodes: [],\n            pageInfo: {\n                hasNextPage: false,\n                endCursor: null\n            }\n        };\n    } catch (error) {\n        console.error(\"Error searching products:\", error);\n        return {\n            nodes: [],\n            pageInfo: {\n                hasNextPage: false,\n                endCursor: null\n            }\n        };\n    }\n}\n/**\n * Get a single product by ID\n * @param id Product ID\n * @returns Product data\n */ async function getProduct(id) {\n    const productQuery = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject22());\n    try {\n        const data = await graphQLClient.request(productQuery, {\n            id\n        });\n        return data.product;\n    } catch (error) {\n        console.error(\"Error fetching product:\", error);\n        throw new Error(\"Failed to fetch product\");\n    }\n}\nasync function getCategoryProducts(slug) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    try {\n        const { first = 20 } = options;\n        const data = await graphQLClient.request(QUERY_CATEGORY_PRODUCTS, {\n            slug,\n            first\n        });\n        return (data === null || data === void 0 ? void 0 : data.productCategory) || null;\n    } catch (error) {\n        console.error(\"Error fetching category products with slug \".concat(slug, \":\"), error);\n        return null;\n    }\n}\n// Customer Mutations\nconst CREATE_CUSTOMER_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject23());\nconst UPDATE_CUSTOMER_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject24());\nconst GET_CUSTOMER_QUERY = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject25());\nconst CREATE_ADDRESS_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject26());\nconst UPDATE_ADDRESS_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject27());\nconst DELETE_ADDRESS_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject28());\nconst SET_DEFAULT_ADDRESS_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject29());\nconst UPDATE_CART_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject30());\n/**\n * Update customer profile\n */ async function updateCustomer(token, customerData) {\n    try {\n        console.log(\"Updating customer with data:\", customerData);\n        console.log(\"Using token:\", token ? \"Token present\" : \"No token\");\n        // Create a new client with the auth token\n        const client = new graphql_request__WEBPACK_IMPORTED_MODULE_1__.GraphQLClient(endpoint, {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Accept\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        const variables = {\n            input: {\n                clientMutationId: \"updateCustomer\",\n                ...customerData\n            }\n        };\n        console.log(\"GraphQL variables:\", variables);\n        const response = await client.request(UPDATE_CUSTOMER_MUTATION, variables);\n        console.log(\"GraphQL response:\", response);\n        if (response.updateCustomer.customerUserErrors && response.updateCustomer.customerUserErrors.length > 0) {\n            const errorMessage = response.updateCustomer.customerUserErrors[0].message;\n            console.error(\"Customer update error:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        return response.updateCustomer;\n    } catch (error) {\n        console.error(\"Error updating customer:\", error);\n        throw error;\n    }\n}\n/**\n * Create a new address for the customer\n */ async function createAddress(token, address) {\n    try {\n        // Create a new client with the auth token\n        const client = new graphql_request__WEBPACK_IMPORTED_MODULE_1__.GraphQLClient(endpoint, {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Accept\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        // Determine if this is a billing or shipping address\n        const addressType = address.addressType || \"shipping\";\n        const variables = {\n            input: {\n                [\"\".concat(addressType)]: {\n                    firstName: address.firstName || \"\",\n                    lastName: address.lastName || \"\",\n                    company: address.company || \"\",\n                    address1: address.address1 || \"\",\n                    address2: address.address2 || \"\",\n                    city: address.city || \"\",\n                    state: address.province || \"\",\n                    postcode: address.zip || \"\",\n                    country: address.country || \"\",\n                    ...addressType === \"billing\" ? {\n                        email: address.email || \"\",\n                        phone: address.phone || \"\"\n                    } : {}\n                }\n            }\n        };\n        const response = await client.request(CREATE_ADDRESS_MUTATION, variables);\n        return {\n            customerAddress: response.updateCustomer.customer[addressType],\n            customerUserErrors: []\n        };\n    } catch (error) {\n        console.error(\"Error creating address:\", error);\n        throw error;\n    }\n}\n/**\n * Update an existing address\n */ async function updateAddress(token, id, address) {\n    try {\n        // Create a new client with the auth token\n        const client = new graphql_request__WEBPACK_IMPORTED_MODULE_1__.GraphQLClient(endpoint, {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Accept\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        // Determine if this is a billing or shipping address\n        const addressType = address.addressType || \"shipping\";\n        const variables = {\n            input: {\n                [\"\".concat(addressType)]: {\n                    firstName: address.firstName || \"\",\n                    lastName: address.lastName || \"\",\n                    company: address.company || \"\",\n                    address1: address.address1 || \"\",\n                    address2: address.address2 || \"\",\n                    city: address.city || \"\",\n                    state: address.province || \"\",\n                    postcode: address.zip || \"\",\n                    country: address.country || \"\",\n                    ...addressType === \"billing\" ? {\n                        email: address.email || \"\",\n                        phone: address.phone || \"\"\n                    } : {}\n                }\n            }\n        };\n        const response = await client.request(UPDATE_ADDRESS_MUTATION, variables);\n        return {\n            customerAddress: response.updateCustomer.customer[addressType],\n            customerUserErrors: []\n        };\n    } catch (error) {\n        console.error(\"Error updating address:\", error);\n        throw error;\n    }\n}\n/**\n * Delete an address\n * Note: In WooCommerce, we don't actually delete addresses but clear them\n */ async function deleteAddress(token, id) {\n    try {\n        // Create a new client with the auth token\n        const client = new graphql_request__WEBPACK_IMPORTED_MODULE_1__.GraphQLClient(endpoint, {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Accept\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        // Get the current customer to determine which address to clear\n        const customer = await getCustomer(token);\n        // Determine if this is a billing or shipping address\n        // In this implementation, we're using the id to determine which address to clear\n        // This is a simplification - you might need a different approach\n        const addressType = id === \"billing\" ? \"billing\" : \"shipping\";\n        const variables = {\n            input: {\n                [\"\".concat(addressType)]: {\n                    firstName: \"\",\n                    lastName: \"\",\n                    company: \"\",\n                    address1: \"\",\n                    address2: \"\",\n                    city: \"\",\n                    state: \"\",\n                    postcode: \"\",\n                    country: \"\",\n                    ...addressType === \"billing\" ? {\n                        email: customer.email || \"\",\n                        phone: \"\"\n                    } : {}\n                }\n            }\n        };\n        const response = await client.request(DELETE_ADDRESS_MUTATION, variables);\n        return {\n            deletedCustomerAddressId: id,\n            customerUserErrors: []\n        };\n    } catch (error) {\n        console.error(\"Error deleting address:\", error);\n        throw error;\n    }\n}\n/**\n * Set default address\n * Note: In WooCommerce, the concept of \"default\" address is different\n * This implementation copies the address from one type to another\n */ async function setDefaultAddress(token, addressId) {\n    try {\n        // Create a new client with the auth token\n        const client = new graphql_request__WEBPACK_IMPORTED_MODULE_1__.GraphQLClient(endpoint, {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Accept\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        });\n        // Get the current customer\n        const customer = await getCustomer(token);\n        // Determine source and target address types\n        // This is a simplification - you might need a different approach\n        const sourceType = addressId === \"billing\" ? \"billing\" : \"shipping\";\n        const targetType = sourceType === \"billing\" ? \"shipping\" : \"billing\";\n        // Copy the address from source to target\n        const sourceAddress = customer[sourceType];\n        const variables = {\n            input: {\n                [\"\".concat(targetType)]: {\n                    ...sourceAddress,\n                    ...targetType === \"billing\" ? {\n                        email: customer.email || \"\",\n                        phone: sourceAddress.phone || \"\"\n                    } : {}\n                }\n            }\n        };\n        const response = await client.request(SET_DEFAULT_ADDRESS_MUTATION, variables);\n        return {\n            customer: response.updateCustomer.customer,\n            customerUserErrors: []\n        };\n    } catch (error) {\n        console.error(\"Error setting default address:\", error);\n        throw error;\n    }\n}\n/**\n * Update cart items\n */ async function updateCart(items) {\n    try {\n        const variables = {\n            input: {\n                items\n            }\n        };\n        const response = await wooGraphQLFetch({\n            query: UPDATE_CART_MUTATION,\n            variables\n        });\n        return response.updateItemQuantities.cart;\n    } catch (error) {\n        console.error(\"Error updating cart:\", error);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/woocommerce.ts\n"));

/***/ })

}]);