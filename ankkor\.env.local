NEXT_PUBLIC_WORDPRESS_URL=https://maroon-lapwing-781450.hostingersite.com
NEXT_PUBLIC_WOOCOMMERCE_GRAPHQL_URL=https://maroon-lapwing-781450.hostingersite.com/graphql
WOOCOMMERCE_GRAPHQL_URL=https://maroon-lapwing-781450.hostingersite.com/graphql
WOOCOMMERCE_CONSUMER_KEY=ck_4d17fdf284e132b57f8a5ff57c1a1897473ac27e
WOOCOMMERCE_CONSUMER_SECRET=cs_bf0f11b174f675ac0a690b15fe0e486302d9d2f3
WOOCOMMERCE_JWT_SECRET=HHCEVOF]b=lDEeeKx/VH_G,Qq6NMp|(=3>1|-pq0Fe5pK~f+$O#l}1Vz};oLN*OB
WOOCOMMERCE_REVALIDATION_SECRET=v371cf3eo8q3183of3cb3t783dxt3zc33etci
UPSTASH_REDIS_REST_URL=https://fast-crayfish-46451.upstash.io
UPSTASH_REDIS_REST_TOKEN=AbVzAAIjcDE5MWFhZmUyMjg3ZjE0MjJmOGVmYmQ4ZmE0MGFiYWEwOXAxMA
QSTASH_TOKEN=eyJVc2VySUQiOiIyODMwZDAzMC1kZWNjLTQxMDEtYjJiMi0xNGMyNDAzMTMwYTciLCJQYXNzd29yZCI6ImMwMTg0NDRkYzJiYTQ0YzhhZTY5MzJkYjJiZTIwMDMzIn0=
# Commerce Provider
NEXT_PUBLIC_COMMERCE_PROVIDER=woocommerce
NEXT_PUBLIC_BACKEND_URL=https://maroon-lapwing-781450.hostingersite.com
NEXT_PUBLIC_WOOCOMMERCE_URL=https://maroon-lapwing-781450.hostingersite.com

# JWT-to-Cookie Bridge URLs
NEXT_PUBLIC_WP_URL=https://maroon-lapwing-781450.hostingersite.com
NEXT_PUBLIC_WP_CHECKOUT_URL=https://maroon-lapwing-781450.hostingersite.com/checkout/

# Test User Credentials
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=TestPassword123!

# Razorpay Configuration
NEXT_PUBLIC_RAZORPAY_KEY_ID=rzp_test_your_key_id_here
RAZORPAY_KEY_SECRET=your_razorpay_key_secret_here

# WooCommerce Order Creation
WOOCOMMERCE_ORDER_WEBHOOK_SECRET=your_webhook_secret_here

# Shipping Configuration
SHIPPING_PROVIDER=woocommerce
# Alternative: SHIPPING_PROVIDER=delhivery or bluedart

# Email Configuration (for order confirmations)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
