# Backend Endpoints Required for Custom Checkout

The custom checkout implementation requires the following WooCommerce REST API endpoints to be implemented on the WordPress/WooCommerce backend:

## 1. Shipping Rates Endpoint

**Endpoint:** `POST /wp-json/custom/v1/shipping-rates`

**Purpose:** Calculate shipping rates based on pincode and cart items

**Request Body:**
```json
{
  "pincode": "123456",
  "cartItems": [
    {
      "id": "item_id",
      "productId": "123",
      "variationId": "456",
      "name": "Product Name",
      "price": 1000,
      "quantity": 2
    }
  ]
}
```

**Response:**
```json
[
  {
    "id": "standard",
    "name": "Standard Shipping",
    "cost": 50,
    "description": "5-7 business days",
    "estimatedDays": "5-7 days"
  },
  {
    "id": "express",
    "name": "Express Shipping",
    "cost": 150,
    "description": "2-3 business days",
    "estimatedDays": "2-3 days"
  }
]
```

## 2. Create Razorpay Order Endpoint

**Endpoint:** `POST /wp-json/custom/v1/create-razorpay-order`

**Purpose:** Create a Razorpay order for payment processing

**Request Body:**
```json
{
  "amount": 105000,
  "receipt": "order_1234567890",
  "notes": {
    "customer_phone": "+919876543210"
  }
}
```

**Response:**
```json
{
  "id": "order_razorpay_id",
  "entity": "order",
  "amount": 105000,
  "amount_paid": 0,
  "amount_due": 105000,
  "currency": "INR",
  "receipt": "order_1234567890",
  "status": "created",
  "attempts": 0,
  "notes": {
    "customer_phone": "+919876543210"
  },
  "created_at": 1640995200
}
```

## 3. Verify Payment Endpoint

**Endpoint:** `POST /wp-json/custom/v1/verify-payment`

**Purpose:** Verify Razorpay payment and create WooCommerce order

**Request Body:**
```json
{
  "razorpay_payment_id": "pay_razorpay_payment_id",
  "razorpay_order_id": "order_razorpay_id",
  "razorpay_signature": "signature_hash",
  "address": {
    "firstName": "John",
    "lastName": "Doe",
    "address1": "123 Main St",
    "address2": "Apt 4B",
    "city": "Mumbai",
    "state": "Maharashtra",
    "pincode": "400001",
    "phone": "+919876543210"
  },
  "cartItems": [
    {
      "id": "item_id",
      "productId": "123",
      "variationId": "456",
      "quantity": 2
    }
  ],
  "shipping": {
    "id": "standard",
    "name": "Standard Shipping",
    "cost": 50
  }
}
```

**Response:**
```json
{
  "success": true,
  "orderId": "12345",
  "message": "Order created successfully"
}
```

## Implementation Notes

1. **Shipping Rates:** Should integrate with your shipping provider's API or use WooCommerce shipping zones/methods
2. **Razorpay Integration:** Requires Razorpay PHP SDK and proper API key configuration
3. **Payment Verification:** Must verify the Razorpay signature for security
4. **Order Creation:** Should create a proper WooCommerce order with all details
5. **Error Handling:** All endpoints should return appropriate error responses

## Security Considerations

- Validate all input data
- Verify Razorpay signatures properly
- Use nonces for CSRF protection
- Sanitize and escape all data
- Log payment transactions for audit

## WordPress Plugin Structure

These endpoints should be implemented as a custom WordPress plugin with:
- Proper REST API registration
- Authentication handling
- Integration with WooCommerce order system
- Razorpay SDK integration
- Shipping calculation logic
