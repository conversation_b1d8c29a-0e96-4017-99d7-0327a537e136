"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["commons-src_lib_localCartStore_ts-39840d39"],{

/***/ "(app-pages-browser)/./src/lib/localCartStore.ts":
/*!***********************************!*\
  !*** ./src/lib/localCartStore.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearCartAfterCheckout: function() { return /* binding */ clearCartAfterCheckout; },\n/* harmony export */   formatPrice: function() { return /* binding */ formatPrice; },\n/* harmony export */   useLocalCartCount: function() { return /* binding */ useLocalCartCount; },\n/* harmony export */   useLocalCartError: function() { return /* binding */ useLocalCartError; },\n/* harmony export */   useLocalCartItems: function() { return /* binding */ useLocalCartItems; },\n/* harmony export */   useLocalCartLoading: function() { return /* binding */ useLocalCartLoading; },\n/* harmony export */   useLocalCartStore: function() { return /* binding */ useLocalCartStore; },\n/* harmony export */   useLocalCartSubtotal: function() { return /* binding */ useLocalCartSubtotal; },\n/* harmony export */   useLocalCartTotal: function() { return /* binding */ useLocalCartTotal; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/**\n * Local Cart Store for Ankkor E-commerce\n *\n * This implementation uses local storage to persist cart data on the client side.\n * When the user proceeds to checkout, the cart items are sent to WooCommerce\n * using the Store API to create a server-side cart before redirecting to the checkout page.\n */ /* __next_internal_client_entry_do_not_use__ useLocalCartStore,useLocalCartItems,useLocalCartCount,useLocalCartSubtotal,useLocalCartTotal,useLocalCartLoading,useLocalCartError,formatPrice,clearCartAfterCheckout auto */ \n\n// Local storage version to handle migrations\nconst STORAGE_VERSION = 1;\n// Generate a unique ID for cart items\nconst generateItemId = ()=>{\n    return Math.random().toString(36).substring(2, 15);\n};\n// Create the store\nconst useLocalCartStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        // State\n        items: [],\n        itemCount: 0,\n        isLoading: false,\n        error: null,\n        // Actions\n        addToCart: async (item)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const items = get().items;\n                // Normalize price format - remove currency symbols and commas\n                let normalizedPrice = item.price;\n                if (typeof normalizedPrice === \"string\") {\n                    // Remove currency symbol if present\n                    const priceString = normalizedPrice.replace(/[₹$€£]/g, \"\").trim();\n                    // Replace comma with empty string if present (for Indian number format)\n                    normalizedPrice = priceString.replace(/,/g, \"\");\n                }\n                // Create a normalized item with clean price\n                const normalizedItem = {\n                    ...item,\n                    price: normalizedPrice\n                };\n                // Check if the item already exists in the cart\n                const existingItemIndex = items.findIndex((cartItem)=>cartItem.productId === normalizedItem.productId && cartItem.variationId === normalizedItem.variationId);\n                if (existingItemIndex !== -1) {\n                    // If item exists, update quantity\n                    const updatedItems = [\n                        ...items\n                    ];\n                    updatedItems[existingItemIndex].quantity += normalizedItem.quantity;\n                    set({\n                        items: updatedItems,\n                        itemCount: updatedItems.reduce((sum, item)=>sum + item.quantity, 0),\n                        isLoading: false\n                    });\n                } else {\n                    // If item doesn't exist, add it with a new ID\n                    const newItem = {\n                        ...normalizedItem,\n                        id: generateItemId()\n                    };\n                    set({\n                        items: [\n                            ...items,\n                            newItem\n                        ],\n                        itemCount: items.reduce((sum, item)=>sum + item.quantity, 0) + newItem.quantity,\n                        isLoading: false\n                    });\n                }\n                // Show success message\n                console.log(\"Item added to cart successfully\");\n                // Store the updated cart in localStorage immediately to prevent loss\n                if (true) {\n                    try {\n                        const state = {\n                            state: {\n                                items: get().items,\n                                itemCount: get().itemCount,\n                                isLoading: false,\n                                error: null\n                            },\n                            version: STORAGE_VERSION\n                        };\n                        localStorage.setItem(\"ankkor-local-cart\", JSON.stringify(state));\n                    } catch (storageError) {\n                        console.warn(\"Failed to manually persist cart to localStorage:\", storageError);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error adding item to cart:\", error);\n                set({\n                    error: error instanceof Error ? error.message : \"An unknown error occurred\",\n                    isLoading: false\n                });\n            }\n        },\n        updateCartItem: (id, quantity)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const items = get().items;\n                if (quantity <= 0) {\n                    // If quantity is 0 or negative, remove the item\n                    return get().removeCartItem(id);\n                }\n                // Find the item and update its quantity\n                const updatedItems = items.map((item)=>item.id === id ? {\n                        ...item,\n                        quantity\n                    } : item);\n                set({\n                    items: updatedItems,\n                    itemCount: updatedItems.reduce((sum, item)=>sum + item.quantity, 0),\n                    isLoading: false\n                });\n                // Immediately persist to localStorage\n                if (true) {\n                    try {\n                        const state = {\n                            state: {\n                                items: updatedItems,\n                                itemCount: updatedItems.reduce((sum, item)=>sum + item.quantity, 0),\n                                isLoading: false,\n                                error: null\n                            },\n                            version: STORAGE_VERSION\n                        };\n                        localStorage.setItem(\"ankkor-local-cart\", JSON.stringify(state));\n                    } catch (storageError) {\n                        console.warn(\"Failed to manually persist cart update to localStorage:\", storageError);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error updating cart item:\", error);\n                set({\n                    error: error instanceof Error ? error.message : \"An unknown error occurred\",\n                    isLoading: false\n                });\n            }\n        },\n        removeCartItem: (id)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const items = get().items;\n                const updatedItems = items.filter((item)=>item.id !== id);\n                set({\n                    items: updatedItems,\n                    itemCount: updatedItems.reduce((sum, item)=>sum + item.quantity, 0),\n                    isLoading: false\n                });\n                // Immediately persist to localStorage\n                if (true) {\n                    try {\n                        const state = {\n                            state: {\n                                items: updatedItems,\n                                itemCount: updatedItems.reduce((sum, item)=>sum + item.quantity, 0),\n                                isLoading: false,\n                                error: null\n                            },\n                            version: STORAGE_VERSION\n                        };\n                        localStorage.setItem(\"ankkor-local-cart\", JSON.stringify(state));\n                    } catch (storageError) {\n                        console.warn(\"Failed to manually persist cart removal to localStorage:\", storageError);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error removing cart item:\", error);\n                set({\n                    error: error instanceof Error ? error.message : \"An unknown error occurred\",\n                    isLoading: false\n                });\n            }\n        },\n        clearCart: ()=>{\n            set({\n                items: [],\n                itemCount: 0,\n                isLoading: false,\n                error: null\n            });\n            // Immediately persist to localStorage\n            if (true) {\n                try {\n                    const state = {\n                        state: {\n                            items: [],\n                            itemCount: 0,\n                            isLoading: false,\n                            error: null\n                        },\n                        version: STORAGE_VERSION\n                    };\n                    localStorage.setItem(\"ankkor-local-cart\", JSON.stringify(state));\n                } catch (storageError) {\n                    console.warn(\"Failed to manually persist cart clearing to localStorage:\", storageError);\n                }\n            }\n        },\n        setError: (error)=>{\n            set({\n                error\n            });\n        },\n        setIsLoading: (isLoading)=>{\n            set({\n                isLoading\n            });\n        },\n        // Helper methods\n        subtotal: ()=>{\n            const items = get().items;\n            try {\n                const calculatedSubtotal = items.reduce((total, item)=>{\n                    // Handle price with or without currency symbol\n                    let itemPrice = 0;\n                    if (typeof item.price === \"string\") {\n                        // Remove currency symbol if present\n                        const priceString = item.price.replace(/[₹$€£]/g, \"\").trim();\n                        // Replace comma with empty string if present (for Indian number format)\n                        const cleanPrice = priceString.replace(/,/g, \"\");\n                        itemPrice = parseFloat(cleanPrice);\n                    } else {\n                        itemPrice = item.price;\n                    }\n                    if (isNaN(itemPrice)) {\n                        console.warn(\"Invalid price for item \".concat(item.id, \": \").concat(item.price));\n                        return total;\n                    }\n                    return total + itemPrice * item.quantity;\n                }, 0);\n                return isNaN(calculatedSubtotal) ? 0 : calculatedSubtotal;\n            } catch (error) {\n                console.error(\"Error calculating subtotal:\", error);\n                return 0;\n            }\n        },\n        total: ()=>{\n            // For now, total is the same as subtotal\n            // In the future, you could add shipping, tax, etc.\n            const calculatedTotal = get().subtotal();\n            return isNaN(calculatedTotal) ? 0 : calculatedTotal;\n        },\n        // Sync cart with WooCommerce using Store API\n        syncWithWooCommerce: async (authToken)=>{\n            const { items } = get();\n            if (items.length === 0) {\n                throw new Error(\"Cart is empty\");\n            }\n            try {\n                console.log(\"Syncing cart with WooCommerce...\");\n                console.log(\"Auth token provided:\", !!authToken);\n                set({\n                    isLoading: true\n                });\n                // If user is logged in, use the JWT-to-Cookie bridge for seamless checkout\n                if (authToken) {\n                    console.log(\"User is authenticated, using JWT-to-Cookie bridge\");\n                    try {\n                        const checkoutUrl = await createWpSessionAndGetCheckoutUrl(authToken, items);\n                        set({\n                            isLoading: false\n                        });\n                        return checkoutUrl;\n                    } catch (bridgeError) {\n                        console.error(\"JWT-to-Cookie bridge failed:\", bridgeError);\n                        // Fall back to guest checkout if the bridge fails\n                        console.log(\"Falling back to guest checkout...\");\n                    // Continue with guest checkout flow below\n                    }\n                }\n                // For guest users, redirect directly to WooCommerce checkout\n                console.log(\"User is not authenticated, redirecting to WooCommerce checkout\");\n                const baseUrl = \"https://deepskyblue-penguin-370791.hostingersite.com\" || 0;\n                const checkoutUrl = \"\".concat(baseUrl, \"/checkout/\");\n                console.log(\"Guest checkout URL:\", checkoutUrl);\n                set({\n                    isLoading: false\n                });\n                return checkoutUrl;\n            } catch (error) {\n                console.error(\"Error syncing cart with WooCommerce:\", error);\n                set({\n                    isLoading: false\n                });\n                // Fallback approach: use URL parameters to build cart\n                try {\n                    console.log(\"Attempting fallback method for cart sync...\");\n                    const baseUrl = \"https://deepskyblue-penguin-370791.hostingersite.com\" || 0;\n                    // Build URL with add-to-cart parameters for each item\n                    let checkoutUrl = \"\".concat(baseUrl, \"/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1\");\n                    // Add each item as a URL parameter\n                    items.forEach((item, index)=>{\n                        if (index === 0) {\n                            checkoutUrl += \"&add-to-cart=\".concat(item.productId, \"&quantity=\").concat(item.quantity);\n                        } else {\n                            // For WooCommerce, additional items need a different format\n                            checkoutUrl += \"&add-to-cart[\".concat(index, \"]=\").concat(item.productId, \"&quantity[\").concat(index, \"]=\").concat(item.quantity);\n                        }\n                        // Add variation ID if present\n                        if (item.variationId) {\n                            checkoutUrl += \"&variation_id=\".concat(item.variationId);\n                        }\n                    });\n                    console.log(\"Fallback checkout URL:\", checkoutUrl);\n                    return checkoutUrl;\n                } catch (fallbackError) {\n                    console.error(\"Fallback method failed:\", fallbackError);\n                    throw new Error(\"Failed to sync cart with WooCommerce. Please try again or contact support.\");\n                }\n            }\n        }\n    }), {\n    name: \"ankkor-local-cart\",\n    version: STORAGE_VERSION\n}));\n// Helper hooks\nconst useLocalCartItems = ()=>useLocalCartStore((state)=>state.items);\nconst useLocalCartCount = ()=>useLocalCartStore((state)=>state.itemCount);\nconst useLocalCartSubtotal = ()=>useLocalCartStore((state)=>state.subtotal());\nconst useLocalCartTotal = ()=>useLocalCartStore((state)=>state.total());\nconst useLocalCartLoading = ()=>useLocalCartStore((state)=>state.isLoading);\nconst useLocalCartError = ()=>useLocalCartStore((state)=>state.error);\n// Helper functions\nconst formatPrice = function(price) {\n    let currencyCode = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"INR\";\n    const amount = typeof price === \"string\" ? parseFloat(price) : price;\n    return new Intl.NumberFormat(\"en-IN\", {\n        style: \"currency\",\n        currency: currencyCode,\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n    }).format(amount);\n};\n// Clear cart after successful checkout\nconst clearCartAfterCheckout = ()=>{\n    useLocalCartStore.getState().clearCart();\n// Also reset the cart token to ensure a fresh cart for the next session\n// cartSession.resetCartToken(); // This line was removed as per the edit hint\n};\n/**\n * Create WordPress session from JWT token and get the checkout URL\n * This implements the JWT-to-Cookie Bridge for seamless checkout experience\n * @param authToken The JWT authentication token\n * @param items Cart items to include in checkout\n * @returns The WooCommerce checkout URL\n */ async function createWpSessionAndGetCheckoutUrl(authToken, items) {\n    if (!authToken) {\n        throw new Error(\"Authentication token is required\");\n    }\n    const wpUrl = \"https://deepskyblue-penguin-370791.hostingersite.com\";\n    const checkoutUrl = \"https://deepskyblue-penguin-370791.hostingersite.com/checkout/\";\n    if (!wpUrl || !checkoutUrl) {\n        throw new Error(\"WordPress or checkout URL not configured. Check your environment variables.\");\n    }\n    try {\n        console.log(\"Creating WordPress session from JWT token...\");\n        console.log(\"Using endpoint:\", \"\".concat(wpUrl, \"/wp-json/headless/v1/create-wp-session\"));\n        console.log(\"Token length:\", authToken.length);\n        console.log(\"Token preview:\", authToken.substring(0, 20) + \"...\");\n        // Call the custom WordPress endpoint to create a session from JWT\n        const response = await fetch(\"\".concat(wpUrl, \"/wp-json/headless/v1/create-wp-session\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(authToken)\n            },\n            // THIS IS THE CRITICAL LINE - Include token in request body as well\n            body: JSON.stringify({\n                token: authToken\n            }),\n            credentials: \"include\"\n        });\n        console.log(\"Response status:\", response.status);\n        console.log(\"Response headers:\", Object.fromEntries(response.headers.entries()));\n        if (!response.ok) {\n            let errorMessage = \"HTTP \".concat(response.status, \": \").concat(response.statusText);\n            try {\n                const errorData = await response.json();\n                errorMessage = errorData.message || errorData.code || errorMessage;\n                console.error(\"Error response data:\", errorData);\n            } catch (parseError) {\n                console.error(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(\"Failed to create WordPress session: \".concat(errorMessage));\n        }\n        const data = await response.json();\n        console.log(\"Response data:\", data);\n        if (!data.success) {\n            throw new Error(data.message || \"Failed to create WordPress session\");\n        }\n        console.log(\"WordPress session created successfully\");\n        console.log(\"Redirecting to checkout URL:\", checkoutUrl);\n        // For authenticated users, we can directly go to checkout\n        // The server already has the user's session and will load the correct cart\n        return checkoutUrl;\n    } catch (error) {\n        console.error(\"Error creating WordPress session:\", error);\n        // Provide more specific error messages\n        if (error instanceof TypeError && error.message.includes(\"fetch\")) {\n            throw new Error(\"Network error: Could not connect to WordPress. Please check your internet connection.\");\n        }\n        throw new Error(error instanceof Error ? error.message : \"Failed to prepare checkout\");\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvbG9jYWxDYXJ0U3RvcmUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBQTs7Ozs7O0NBTUMsOE5BSWdDO0FBQ1k7QUE0QzdDLDZDQUE2QztBQUM3QyxNQUFNRSxrQkFBa0I7QUFFeEIsc0NBQXNDO0FBQ3RDLE1BQU1DLGlCQUFpQjtJQUNyQixPQUFPQyxLQUFLQyxNQUFNLEdBQUdDLFFBQVEsQ0FBQyxJQUFJQyxTQUFTLENBQUMsR0FBRztBQUNqRDtBQUVBLG1CQUFtQjtBQUNaLE1BQU1DLG9CQUFvQlIsK0NBQU1BLEdBQ3JDQywyREFBT0EsQ0FDTCxDQUFDUSxLQUFLQyxNQUFTO1FBQ2IsUUFBUTtRQUNSQyxPQUFPLEVBQUU7UUFDVEMsV0FBVztRQUNYQyxXQUFXO1FBQ1hDLE9BQU87UUFFUCxVQUFVO1FBQ1ZDLFdBQVcsT0FBT0M7WUFDaEJQLElBQUk7Z0JBQUVJLFdBQVc7Z0JBQU1DLE9BQU87WUFBSztZQUNuQyxJQUFJO2dCQUNGLE1BQU1ILFFBQVFELE1BQU1DLEtBQUs7Z0JBRXpCLDhEQUE4RDtnQkFDOUQsSUFBSU0sa0JBQWtCRCxLQUFLRSxLQUFLO2dCQUNoQyxJQUFJLE9BQU9ELG9CQUFvQixVQUFVO29CQUN2QyxvQ0FBb0M7b0JBQ3BDLE1BQU1FLGNBQWNGLGdCQUFnQkcsT0FBTyxDQUFDLFdBQVcsSUFBSUMsSUFBSTtvQkFDL0Qsd0VBQXdFO29CQUN4RUosa0JBQWtCRSxZQUFZQyxPQUFPLENBQUMsTUFBTTtnQkFDOUM7Z0JBRUEsNENBQTRDO2dCQUM1QyxNQUFNRSxpQkFBaUI7b0JBQ3JCLEdBQUdOLElBQUk7b0JBQ1BFLE9BQU9EO2dCQUNUO2dCQUVBLCtDQUErQztnQkFDL0MsTUFBTU0sb0JBQW9CWixNQUFNYSxTQUFTLENBQ3ZDLENBQUNDLFdBQ0NBLFNBQVNDLFNBQVMsS0FBS0osZUFBZUksU0FBUyxJQUMvQ0QsU0FBU0UsV0FBVyxLQUFLTCxlQUFlSyxXQUFXO2dCQUd2RCxJQUFJSixzQkFBc0IsQ0FBQyxHQUFHO29CQUM1QixrQ0FBa0M7b0JBQ2xDLE1BQU1LLGVBQWU7MkJBQUlqQjtxQkFBTTtvQkFDL0JpQixZQUFZLENBQUNMLGtCQUFrQixDQUFDTSxRQUFRLElBQUlQLGVBQWVPLFFBQVE7b0JBRW5FcEIsSUFBSTt3QkFDRkUsT0FBT2lCO3dCQUNQaEIsV0FBV2dCLGFBQWFFLE1BQU0sQ0FBQyxDQUFDQyxLQUFLZixPQUFTZSxNQUFNZixLQUFLYSxRQUFRLEVBQUU7d0JBQ25FaEIsV0FBVztvQkFDYjtnQkFDRixPQUFPO29CQUNMLDhDQUE4QztvQkFDOUMsTUFBTW1CLFVBQVU7d0JBQ2QsR0FBR1YsY0FBYzt3QkFDakJXLElBQUk5QjtvQkFDTjtvQkFFQU0sSUFBSTt3QkFDRkUsT0FBTzsrQkFBSUE7NEJBQU9xQjt5QkFBUTt3QkFDMUJwQixXQUFXRCxNQUFNbUIsTUFBTSxDQUFDLENBQUNDLEtBQUtmLE9BQVNlLE1BQU1mLEtBQUthLFFBQVEsRUFBRSxLQUFLRyxRQUFRSCxRQUFRO3dCQUNqRmhCLFdBQVc7b0JBQ2I7Z0JBQ0Y7Z0JBRUEsdUJBQXVCO2dCQUN2QnFCLFFBQVFDLEdBQUcsQ0FBQztnQkFFWixxRUFBcUU7Z0JBQ3JFLElBQUksSUFBa0IsRUFBYTtvQkFDakMsSUFBSTt3QkFDRixNQUFNQyxRQUFROzRCQUNaQSxPQUFPO2dDQUNMekIsT0FBT0QsTUFBTUMsS0FBSztnQ0FDbEJDLFdBQVdGLE1BQU1FLFNBQVM7Z0NBQzFCQyxXQUFXO2dDQUNYQyxPQUFPOzRCQUNUOzRCQUNBdUIsU0FBU25DO3dCQUNYO3dCQUNBb0MsYUFBYUMsT0FBTyxDQUFDLHFCQUFxQkMsS0FBS0MsU0FBUyxDQUFDTDtvQkFDM0QsRUFBRSxPQUFPTSxjQUFjO3dCQUNyQlIsUUFBUVMsSUFBSSxDQUFDLG9EQUFvREQ7b0JBQ25FO2dCQUNGO1lBQ0YsRUFBRSxPQUFPNUIsT0FBTztnQkFDZG9CLFFBQVFwQixLQUFLLENBQUMsOEJBQThCQTtnQkFDNUNMLElBQUk7b0JBQ0ZLLE9BQU9BLGlCQUFpQjhCLFFBQVE5QixNQUFNK0IsT0FBTyxHQUFHO29CQUNoRGhDLFdBQVc7Z0JBQ2I7WUFDRjtRQUNGO1FBRUFpQyxnQkFBZ0IsQ0FBQ2IsSUFBSUo7WUFDbkJwQixJQUFJO2dCQUFFSSxXQUFXO2dCQUFNQyxPQUFPO1lBQUs7WUFDbkMsSUFBSTtnQkFDRixNQUFNSCxRQUFRRCxNQUFNQyxLQUFLO2dCQUN6QixJQUFJa0IsWUFBWSxHQUFHO29CQUNqQixnREFBZ0Q7b0JBQ2hELE9BQU9uQixNQUFNcUMsY0FBYyxDQUFDZDtnQkFDOUI7Z0JBRUEsd0NBQXdDO2dCQUN4QyxNQUFNTCxlQUFlakIsTUFBTXFDLEdBQUcsQ0FBQ2hDLENBQUFBLE9BQzdCQSxLQUFLaUIsRUFBRSxLQUFLQSxLQUFLO3dCQUFFLEdBQUdqQixJQUFJO3dCQUFFYTtvQkFBUyxJQUFJYjtnQkFHM0NQLElBQUk7b0JBQ0ZFLE9BQU9pQjtvQkFDUGhCLFdBQVdnQixhQUFhRSxNQUFNLENBQUMsQ0FBQ0MsS0FBS2YsT0FBU2UsTUFBTWYsS0FBS2EsUUFBUSxFQUFFO29CQUNuRWhCLFdBQVc7Z0JBQ2I7Z0JBRUEsc0NBQXNDO2dCQUN0QyxJQUFJLElBQWtCLEVBQWE7b0JBQ2pDLElBQUk7d0JBQ0YsTUFBTXVCLFFBQVE7NEJBQ1pBLE9BQU87Z0NBQ0x6QixPQUFPaUI7Z0NBQ1BoQixXQUFXZ0IsYUFBYUUsTUFBTSxDQUFDLENBQUNDLEtBQUtmLE9BQVNlLE1BQU1mLEtBQUthLFFBQVEsRUFBRTtnQ0FDbkVoQixXQUFXO2dDQUNYQyxPQUFPOzRCQUNUOzRCQUNBdUIsU0FBU25DO3dCQUNYO3dCQUNBb0MsYUFBYUMsT0FBTyxDQUFDLHFCQUFxQkMsS0FBS0MsU0FBUyxDQUFDTDtvQkFDM0QsRUFBRSxPQUFPTSxjQUFjO3dCQUNyQlIsUUFBUVMsSUFBSSxDQUFDLDJEQUEyREQ7b0JBQzFFO2dCQUNGO1lBQ0YsRUFBRSxPQUFPNUIsT0FBTztnQkFDZG9CLFFBQVFwQixLQUFLLENBQUMsNkJBQTZCQTtnQkFDM0NMLElBQUk7b0JBQ0ZLLE9BQU9BLGlCQUFpQjhCLFFBQVE5QixNQUFNK0IsT0FBTyxHQUFHO29CQUNoRGhDLFdBQVc7Z0JBQ2I7WUFDRjtRQUNGO1FBRUFrQyxnQkFBZ0IsQ0FBQ2Q7WUFDZnhCLElBQUk7Z0JBQUVJLFdBQVc7Z0JBQU1DLE9BQU87WUFBSztZQUNuQyxJQUFJO2dCQUNGLE1BQU1ILFFBQVFELE1BQU1DLEtBQUs7Z0JBQ3pCLE1BQU1pQixlQUFlakIsTUFBTXNDLE1BQU0sQ0FBQ2pDLENBQUFBLE9BQVFBLEtBQUtpQixFQUFFLEtBQUtBO2dCQUV0RHhCLElBQUk7b0JBQ0ZFLE9BQU9pQjtvQkFDUGhCLFdBQVdnQixhQUFhRSxNQUFNLENBQUMsQ0FBQ0MsS0FBS2YsT0FBU2UsTUFBTWYsS0FBS2EsUUFBUSxFQUFFO29CQUNuRWhCLFdBQVc7Z0JBQ2I7Z0JBRUEsc0NBQXNDO2dCQUN0QyxJQUFJLElBQWtCLEVBQWE7b0JBQ2pDLElBQUk7d0JBQ0YsTUFBTXVCLFFBQVE7NEJBQ1pBLE9BQU87Z0NBQ0x6QixPQUFPaUI7Z0NBQ1BoQixXQUFXZ0IsYUFBYUUsTUFBTSxDQUFDLENBQUNDLEtBQUtmLE9BQVNlLE1BQU1mLEtBQUthLFFBQVEsRUFBRTtnQ0FDbkVoQixXQUFXO2dDQUNYQyxPQUFPOzRCQUNUOzRCQUNBdUIsU0FBU25DO3dCQUNYO3dCQUNBb0MsYUFBYUMsT0FBTyxDQUFDLHFCQUFxQkMsS0FBS0MsU0FBUyxDQUFDTDtvQkFDM0QsRUFBRSxPQUFPTSxjQUFjO3dCQUNyQlIsUUFBUVMsSUFBSSxDQUFDLDREQUE0REQ7b0JBQzNFO2dCQUNGO1lBQ0YsRUFBRSxPQUFPNUIsT0FBTztnQkFDZG9CLFFBQVFwQixLQUFLLENBQUMsNkJBQTZCQTtnQkFDM0NMLElBQUk7b0JBQ0ZLLE9BQU9BLGlCQUFpQjhCLFFBQVE5QixNQUFNK0IsT0FBTyxHQUFHO29CQUNoRGhDLFdBQVc7Z0JBQ2I7WUFDRjtRQUNGO1FBRUFxQyxXQUFXO1lBQ1R6QyxJQUFJO2dCQUNGRSxPQUFPLEVBQUU7Z0JBQ1RDLFdBQVc7Z0JBQ1hDLFdBQVc7Z0JBQ1hDLE9BQU87WUFDVDtZQUVBLHNDQUFzQztZQUN0QyxJQUFJLElBQWtCLEVBQWE7Z0JBQ2pDLElBQUk7b0JBQ0YsTUFBTXNCLFFBQVE7d0JBQ1pBLE9BQU87NEJBQ0x6QixPQUFPLEVBQUU7NEJBQ1RDLFdBQVc7NEJBQ1hDLFdBQVc7NEJBQ1hDLE9BQU87d0JBQ1Q7d0JBQ0F1QixTQUFTbkM7b0JBQ1g7b0JBQ0FvQyxhQUFhQyxPQUFPLENBQUMscUJBQXFCQyxLQUFLQyxTQUFTLENBQUNMO2dCQUMzRCxFQUFFLE9BQU9NLGNBQWM7b0JBQ3JCUixRQUFRUyxJQUFJLENBQUMsNkRBQTZERDtnQkFDNUU7WUFDRjtRQUNGO1FBRUFTLFVBQVUsQ0FBQ3JDO1lBQ1RMLElBQUk7Z0JBQUVLO1lBQU07UUFDZDtRQUVBc0MsY0FBYyxDQUFDdkM7WUFDYkosSUFBSTtnQkFBRUk7WUFBVTtRQUNsQjtRQUVBLGlCQUFpQjtRQUNqQndDLFVBQVU7WUFDUixNQUFNMUMsUUFBUUQsTUFBTUMsS0FBSztZQUN6QixJQUFJO2dCQUNGLE1BQU0yQyxxQkFBcUIzQyxNQUFNbUIsTUFBTSxDQUFDLENBQUN5QixPQUFPdkM7b0JBQzlDLCtDQUErQztvQkFDL0MsSUFBSXdDLFlBQVk7b0JBQ2hCLElBQUksT0FBT3hDLEtBQUtFLEtBQUssS0FBSyxVQUFVO3dCQUNsQyxvQ0FBb0M7d0JBQ3BDLE1BQU1DLGNBQWNILEtBQUtFLEtBQUssQ0FBQ0UsT0FBTyxDQUFDLFdBQVcsSUFBSUMsSUFBSTt3QkFDMUQsd0VBQXdFO3dCQUN4RSxNQUFNb0MsYUFBYXRDLFlBQVlDLE9BQU8sQ0FBQyxNQUFNO3dCQUM3Q29DLFlBQVlFLFdBQVdEO29CQUN6QixPQUFPO3dCQUNMRCxZQUFZeEMsS0FBS0UsS0FBSztvQkFDeEI7b0JBRUEsSUFBSXlDLE1BQU1ILFlBQVk7d0JBQ3BCdEIsUUFBUVMsSUFBSSxDQUFDLDBCQUFzQzNCLE9BQVpBLEtBQUtpQixFQUFFLEVBQUMsTUFBZSxPQUFYakIsS0FBS0UsS0FBSzt3QkFDN0QsT0FBT3FDO29CQUNUO29CQUVBLE9BQU9BLFFBQVNDLFlBQVl4QyxLQUFLYSxRQUFRO2dCQUMzQyxHQUFHO2dCQUVILE9BQU84QixNQUFNTCxzQkFBc0IsSUFBSUE7WUFDekMsRUFBRSxPQUFPeEMsT0FBTztnQkFDZG9CLFFBQVFwQixLQUFLLENBQUMsK0JBQStCQTtnQkFDN0MsT0FBTztZQUNUO1FBQ0Y7UUFFQXlDLE9BQU87WUFDTCx5Q0FBeUM7WUFDekMsbURBQW1EO1lBQ25ELE1BQU1LLGtCQUFrQmxELE1BQU0yQyxRQUFRO1lBQ3RDLE9BQU9NLE1BQU1DLG1CQUFtQixJQUFJQTtRQUN0QztRQUVBLDZDQUE2QztRQUM3Q0MscUJBQXFCLE9BQU9DO1lBQzFCLE1BQU0sRUFBRW5ELEtBQUssRUFBRSxHQUFHRDtZQUNsQixJQUFJQyxNQUFNb0QsTUFBTSxLQUFLLEdBQUc7Z0JBQ3RCLE1BQU0sSUFBSW5CLE1BQU07WUFDbEI7WUFFQSxJQUFJO2dCQUNGVixRQUFRQyxHQUFHLENBQUM7Z0JBQ1pELFFBQVFDLEdBQUcsQ0FBQyx3QkFBd0IsQ0FBQyxDQUFDMkI7Z0JBQ3RDckQsSUFBSTtvQkFBRUksV0FBVztnQkFBSztnQkFFdEIsMkVBQTJFO2dCQUMzRSxJQUFJaUQsV0FBVztvQkFDYjVCLFFBQVFDLEdBQUcsQ0FBQztvQkFDWixJQUFJO3dCQUNGLE1BQU02QixjQUFjLE1BQU1DLGlDQUFpQ0gsV0FBV25EO3dCQUN0RUYsSUFBSTs0QkFBRUksV0FBVzt3QkFBTTt3QkFDdkIsT0FBT21EO29CQUNULEVBQUUsT0FBT0UsYUFBYTt3QkFDcEJoQyxRQUFRcEIsS0FBSyxDQUFDLGdDQUFnQ29EO3dCQUM5QyxrREFBa0Q7d0JBQ2xEaEMsUUFBUUMsR0FBRyxDQUFDO29CQUNaLDBDQUEwQztvQkFDNUM7Z0JBQ0Y7Z0JBRUEsNkRBQTZEO2dCQUM3REQsUUFBUUMsR0FBRyxDQUFDO2dCQUNaLE1BQU1nQyxVQUFVQyxzREFBdUMsSUFBSUEsQ0FBcUM7Z0JBQ2hHLE1BQU1KLGNBQWMsR0FBVyxPQUFSRyxTQUFRO2dCQUMvQmpDLFFBQVFDLEdBQUcsQ0FBQyx1QkFBdUI2QjtnQkFDbkN2RCxJQUFJO29CQUFFSSxXQUFXO2dCQUFNO2dCQUN2QixPQUFPbUQ7WUFFVCxFQUFFLE9BQU9sRCxPQUFPO2dCQUNkb0IsUUFBUXBCLEtBQUssQ0FBQyx3Q0FBd0NBO2dCQUN0REwsSUFBSTtvQkFBRUksV0FBVztnQkFBTTtnQkFFdkIsc0RBQXNEO2dCQUN0RCxJQUFJO29CQUNGcUIsUUFBUUMsR0FBRyxDQUFDO29CQUNaLE1BQU1nQyxVQUFVQyxzREFBdUMsSUFBSUEsQ0FBcUM7b0JBRWhHLHNEQUFzRDtvQkFDdEQsSUFBSUosY0FBYyxHQUFXLE9BQVJHLFNBQVE7b0JBRTdCLG1DQUFtQztvQkFDbkN4RCxNQUFNNkQsT0FBTyxDQUFDLENBQUN4RCxNQUFNeUQ7d0JBQ25CLElBQUlBLFVBQVUsR0FBRzs0QkFDZlQsZUFBZSxnQkFBMkNoRCxPQUEzQkEsS0FBS1UsU0FBUyxFQUFDLGNBQTBCLE9BQWRWLEtBQUthLFFBQVE7d0JBQ3pFLE9BQU87NEJBQ0wsNERBQTREOzRCQUM1RG1DLGVBQWUsZ0JBQTBCaEQsT0FBVnlELE9BQU0sTUFBK0JBLE9BQTNCekQsS0FBS1UsU0FBUyxFQUFDLGNBQXNCVixPQUFWeUQsT0FBTSxNQUFrQixPQUFkekQsS0FBS2EsUUFBUTt3QkFDN0Y7d0JBRUEsOEJBQThCO3dCQUM5QixJQUFJYixLQUFLVyxXQUFXLEVBQUU7NEJBQ3BCcUMsZUFBZSxpQkFBa0MsT0FBakJoRCxLQUFLVyxXQUFXO3dCQUNsRDtvQkFDRjtvQkFFQU8sUUFBUUMsR0FBRyxDQUFDLDBCQUEwQjZCO29CQUN0QyxPQUFPQTtnQkFDVCxFQUFFLE9BQU9VLGVBQWU7b0JBQ3RCeEMsUUFBUXBCLEtBQUssQ0FBQywyQkFBMkI0RDtvQkFDekMsTUFBTSxJQUFJOUIsTUFBTTtnQkFDbEI7WUFDRjtRQUNGO0lBQ0YsSUFDQTtJQUNFK0IsTUFBTTtJQUNOdEMsU0FBU25DO0FBQ1gsSUFFRjtBQUVGLGVBQWU7QUFDUixNQUFNMEUsb0JBQW9CLElBQU1wRSxrQkFBa0I0QixDQUFBQSxRQUFTQSxNQUFNekIsS0FBSyxFQUFFO0FBQ3hFLE1BQU1rRSxvQkFBb0IsSUFBTXJFLGtCQUFrQjRCLENBQUFBLFFBQVNBLE1BQU14QixTQUFTLEVBQUU7QUFDNUUsTUFBTWtFLHVCQUF1QixJQUFNdEUsa0JBQWtCNEIsQ0FBQUEsUUFBU0EsTUFBTWlCLFFBQVEsSUFBSTtBQUNoRixNQUFNMEIsb0JBQW9CLElBQU12RSxrQkFBa0I0QixDQUFBQSxRQUFTQSxNQUFNbUIsS0FBSyxJQUFJO0FBQzFFLE1BQU15QixzQkFBc0IsSUFBTXhFLGtCQUFrQjRCLENBQUFBLFFBQVNBLE1BQU12QixTQUFTLEVBQUU7QUFDOUUsTUFBTW9FLG9CQUFvQixJQUFNekUsa0JBQWtCNEIsQ0FBQUEsUUFBU0EsTUFBTXRCLEtBQUssRUFBRTtBQUUvRSxtQkFBbUI7QUFDWixNQUFNb0UsY0FBYyxTQUFDaEU7UUFBd0JpRSxnRkFBZTtJQUNqRSxNQUFNQyxTQUFTLE9BQU9sRSxVQUFVLFdBQVd3QyxXQUFXeEMsU0FBU0E7SUFDL0QsT0FBTyxJQUFJbUUsS0FBS0MsWUFBWSxDQUFDLFNBQVM7UUFDcENDLE9BQU87UUFDUEMsVUFBVUw7UUFDVk0sdUJBQXVCO1FBQ3ZCQyx1QkFBdUI7SUFDekIsR0FBR0MsTUFBTSxDQUFDUDtBQUNaLEVBQUU7QUFFRix1Q0FBdUM7QUFDaEMsTUFBTVEseUJBQXlCO0lBQ3BDcEYsa0JBQWtCcUYsUUFBUSxHQUFHM0MsU0FBUztBQUV0Qyx3RUFBd0U7QUFDeEUsOEVBQThFO0FBQ2hGLEVBQUU7QUFJRjs7Ozs7O0NBTUMsR0FDRCxlQUFlZSxpQ0FBaUNILFNBQWlCLEVBQUVuRCxLQUFpQjtJQUNsRixJQUFJLENBQUNtRCxXQUFXO1FBQ2QsTUFBTSxJQUFJbEIsTUFBTTtJQUNsQjtJQUVBLE1BQU1rRCxRQUFRMUIsc0RBQThCO0lBQzVDLE1BQU1KLGNBQWNJLGdFQUF1QztJQUUzRCxJQUFJLENBQUMwQixTQUFTLENBQUM5QixhQUFhO1FBQzFCLE1BQU0sSUFBSXBCLE1BQU07SUFDbEI7SUFFQSxJQUFJO1FBQ0ZWLFFBQVFDLEdBQUcsQ0FBQztRQUNaRCxRQUFRQyxHQUFHLENBQUMsbUJBQW1CLEdBQVMsT0FBTjJELE9BQU07UUFDeEM1RCxRQUFRQyxHQUFHLENBQUMsaUJBQWlCMkIsVUFBVUMsTUFBTTtRQUM3QzdCLFFBQVFDLEdBQUcsQ0FBQyxrQkFBa0IyQixVQUFVdkQsU0FBUyxDQUFDLEdBQUcsTUFBTTtRQUUzRCxrRUFBa0U7UUFDbEUsTUFBTTBGLFdBQVcsTUFBTUMsTUFBTSxHQUFTLE9BQU5KLE9BQU0sMkNBQXlDO1lBQzdFSyxRQUFRO1lBQ1JDLFNBQVM7Z0JBQ1AsZ0JBQWdCO2dCQUNoQixpQkFBaUIsVUFBb0IsT0FBVnRDO1lBQzdCO1lBQ0Esb0VBQW9FO1lBQ3BFdUMsTUFBTTdELEtBQUtDLFNBQVMsQ0FBQztnQkFBRTZELE9BQU94QztZQUFVO1lBQ3hDeUMsYUFBYTtRQUNmO1FBRUFyRSxRQUFRQyxHQUFHLENBQUMsb0JBQW9COEQsU0FBU08sTUFBTTtRQUMvQ3RFLFFBQVFDLEdBQUcsQ0FBQyxxQkFBcUJzRSxPQUFPQyxXQUFXLENBQUNULFNBQVNHLE9BQU8sQ0FBQ08sT0FBTztRQUU1RSxJQUFJLENBQUNWLFNBQVNXLEVBQUUsRUFBRTtZQUNoQixJQUFJQyxlQUFlLFFBQTRCWixPQUFwQkEsU0FBU08sTUFBTSxFQUFDLE1BQXdCLE9BQXBCUCxTQUFTYSxVQUFVO1lBQ2xFLElBQUk7Z0JBQ0YsTUFBTUMsWUFBWSxNQUFNZCxTQUFTZSxJQUFJO2dCQUNyQ0gsZUFBZUUsVUFBVWxFLE9BQU8sSUFBSWtFLFVBQVVFLElBQUksSUFBSUo7Z0JBQ3REM0UsUUFBUXBCLEtBQUssQ0FBQyx3QkFBd0JpRztZQUN4QyxFQUFFLE9BQU9HLFlBQVk7Z0JBQ25CaEYsUUFBUXBCLEtBQUssQ0FBQyxtQ0FBbUNvRztZQUNuRDtZQUNBLE1BQU0sSUFBSXRFLE1BQU0sdUNBQW9ELE9BQWJpRTtRQUN6RDtRQUVBLE1BQU1NLE9BQU8sTUFBTWxCLFNBQVNlLElBQUk7UUFDaEM5RSxRQUFRQyxHQUFHLENBQUMsa0JBQWtCZ0Y7UUFFOUIsSUFBSSxDQUFDQSxLQUFLQyxPQUFPLEVBQUU7WUFDakIsTUFBTSxJQUFJeEUsTUFBTXVFLEtBQUt0RSxPQUFPLElBQUk7UUFDbEM7UUFFQVgsUUFBUUMsR0FBRyxDQUFDO1FBQ1pELFFBQVFDLEdBQUcsQ0FBQyxnQ0FBZ0M2QjtRQUU1QywwREFBMEQ7UUFDMUQsMkVBQTJFO1FBQzNFLE9BQU9BO0lBQ1QsRUFBRSxPQUFPbEQsT0FBTztRQUNkb0IsUUFBUXBCLEtBQUssQ0FBQyxxQ0FBcUNBO1FBRW5ELHVDQUF1QztRQUN2QyxJQUFJQSxpQkFBaUJ1RyxhQUFhdkcsTUFBTStCLE9BQU8sQ0FBQ3lFLFFBQVEsQ0FBQyxVQUFVO1lBQ2pFLE1BQU0sSUFBSTFFLE1BQU07UUFDbEI7UUFFQSxNQUFNLElBQUlBLE1BQU05QixpQkFBaUI4QixRQUFROUIsTUFBTStCLE9BQU8sR0FBRztJQUMzRDtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9saWIvbG9jYWxDYXJ0U3RvcmUudHM/NTM3MCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIExvY2FsIENhcnQgU3RvcmUgZm9yIEFua2tvciBFLWNvbW1lcmNlXG4gKlxuICogVGhpcyBpbXBsZW1lbnRhdGlvbiB1c2VzIGxvY2FsIHN0b3JhZ2UgdG8gcGVyc2lzdCBjYXJ0IGRhdGEgb24gdGhlIGNsaWVudCBzaWRlLlxuICogV2hlbiB0aGUgdXNlciBwcm9jZWVkcyB0byBjaGVja291dCwgdGhlIGNhcnQgaXRlbXMgYXJlIHNlbnQgdG8gV29vQ29tbWVyY2VcbiAqIHVzaW5nIHRoZSBTdG9yZSBBUEkgdG8gY3JlYXRlIGEgc2VydmVyLXNpZGUgY2FydCBiZWZvcmUgcmVkaXJlY3RpbmcgdG8gdGhlIGNoZWNrb3V0IHBhZ2UuXG4gKi9cblxuJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBjcmVhdGUgfSBmcm9tICd6dXN0YW5kJztcbmltcG9ydCB7IHBlcnNpc3QgfSBmcm9tICd6dXN0YW5kL21pZGRsZXdhcmUnO1xuXG4vLyBUeXBlIGRlZmluaXRpb25zXG5leHBvcnQgaW50ZXJmYWNlIENhcnRJdGVtIHtcbiAgaWQ6IHN0cmluZztcbiAgcHJvZHVjdElkOiBzdHJpbmc7XG4gIHZhcmlhdGlvbklkPzogc3RyaW5nO1xuICBxdWFudGl0eTogbnVtYmVyO1xuICBuYW1lOiBzdHJpbmc7XG4gIHByaWNlOiBzdHJpbmc7XG4gIGltYWdlPzoge1xuICAgIHVybDogc3RyaW5nO1xuICAgIGFsdFRleHQ/OiBzdHJpbmc7XG4gIH07XG4gIGF0dHJpYnV0ZXM/OiBBcnJheTx7XG4gICAgbmFtZTogc3RyaW5nO1xuICAgIHZhbHVlOiBzdHJpbmc7XG4gIH0+O1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIExvY2FsQ2FydCB7XG4gIGl0ZW1zOiBDYXJ0SXRlbVtdO1xuICBpdGVtQ291bnQ6IG51bWJlcjtcbiAgaXNMb2FkaW5nOiBib29sZWFuO1xuICBlcnJvcjogc3RyaW5nIHwgbnVsbDtcbn1cblxuLy8gQWN0aW9ucyBpbnRlcmZhY2VcbmludGVyZmFjZSBDYXJ0QWN0aW9ucyB7XG4gIGFkZFRvQ2FydDogKGl0ZW06IE9taXQ8Q2FydEl0ZW0sICdpZCc+KSA9PiBQcm9taXNlPHZvaWQ+O1xuICB1cGRhdGVDYXJ0SXRlbTogKGlkOiBzdHJpbmcsIHF1YW50aXR5OiBudW1iZXIpID0+IHZvaWQ7XG4gIHJlbW92ZUNhcnRJdGVtOiAoaWQ6IHN0cmluZykgPT4gdm9pZDtcbiAgY2xlYXJDYXJ0OiAoKSA9PiB2b2lkO1xuICBzZXRFcnJvcjogKGVycm9yOiBzdHJpbmcgfCBudWxsKSA9PiB2b2lkO1xuICBzZXRJc0xvYWRpbmc6IChpc0xvYWRpbmc6IGJvb2xlYW4pID0+IHZvaWQ7XG4gIHN5bmNXaXRoV29vQ29tbWVyY2U6IChhdXRoVG9rZW4/OiBzdHJpbmcgfCBudWxsKSA9PiBQcm9taXNlPHN0cmluZyB8IG51bGw+OyAvLyBSZXR1cm5zIGNoZWNrb3V0IFVSTFxufVxuXG4vLyBDYXJ0IHN0b3JlIGludGVyZmFjZVxuZXhwb3J0IGludGVyZmFjZSBMb2NhbENhcnRTdG9yZSBleHRlbmRzIExvY2FsQ2FydCwgQ2FydEFjdGlvbnMge1xuICBzdWJ0b3RhbDogKCkgPT4gbnVtYmVyO1xuICB0b3RhbDogKCkgPT4gbnVtYmVyO1xufVxuXG4vLyBMb2NhbCBzdG9yYWdlIHZlcnNpb24gdG8gaGFuZGxlIG1pZ3JhdGlvbnNcbmNvbnN0IFNUT1JBR0VfVkVSU0lPTiA9IDE7XG5cbi8vIEdlbmVyYXRlIGEgdW5pcXVlIElEIGZvciBjYXJ0IGl0ZW1zXG5jb25zdCBnZW5lcmF0ZUl0ZW1JZCA9ICgpOiBzdHJpbmcgPT4ge1xuICByZXR1cm4gTWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyaW5nKDIsIDE1KTtcbn07XG5cbi8vIENyZWF0ZSB0aGUgc3RvcmVcbmV4cG9ydCBjb25zdCB1c2VMb2NhbENhcnRTdG9yZSA9IGNyZWF0ZTxMb2NhbENhcnRTdG9yZT4oKShcbiAgcGVyc2lzdChcbiAgICAoc2V0LCBnZXQpID0+ICh7XG4gICAgICAvLyBTdGF0ZVxuICAgICAgaXRlbXM6IFtdLFxuICAgICAgaXRlbUNvdW50OiAwLFxuICAgICAgaXNMb2FkaW5nOiBmYWxzZSxcbiAgICAgIGVycm9yOiBudWxsLFxuXG4gICAgICAvLyBBY3Rpb25zXG4gICAgICBhZGRUb0NhcnQ6IGFzeW5jIChpdGVtKSA9PiB7XG4gICAgICAgIHNldCh7IGlzTG9hZGluZzogdHJ1ZSwgZXJyb3I6IG51bGwgfSk7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgY29uc3QgaXRlbXMgPSBnZXQoKS5pdGVtcztcblxuICAgICAgICAgIC8vIE5vcm1hbGl6ZSBwcmljZSBmb3JtYXQgLSByZW1vdmUgY3VycmVuY3kgc3ltYm9scyBhbmQgY29tbWFzXG4gICAgICAgICAgbGV0IG5vcm1hbGl6ZWRQcmljZSA9IGl0ZW0ucHJpY2U7XG4gICAgICAgICAgaWYgKHR5cGVvZiBub3JtYWxpemVkUHJpY2UgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgICAgICAvLyBSZW1vdmUgY3VycmVuY3kgc3ltYm9sIGlmIHByZXNlbnRcbiAgICAgICAgICAgIGNvbnN0IHByaWNlU3RyaW5nID0gbm9ybWFsaXplZFByaWNlLnJlcGxhY2UoL1vigrkk4oKswqNdL2csICcnKS50cmltKCk7XG4gICAgICAgICAgICAvLyBSZXBsYWNlIGNvbW1hIHdpdGggZW1wdHkgc3RyaW5nIGlmIHByZXNlbnQgKGZvciBJbmRpYW4gbnVtYmVyIGZvcm1hdClcbiAgICAgICAgICAgIG5vcm1hbGl6ZWRQcmljZSA9IHByaWNlU3RyaW5nLnJlcGxhY2UoLywvZywgJycpO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIC8vIENyZWF0ZSBhIG5vcm1hbGl6ZWQgaXRlbSB3aXRoIGNsZWFuIHByaWNlXG4gICAgICAgICAgY29uc3Qgbm9ybWFsaXplZEl0ZW0gPSB7XG4gICAgICAgICAgICAuLi5pdGVtLFxuICAgICAgICAgICAgcHJpY2U6IG5vcm1hbGl6ZWRQcmljZVxuICAgICAgICAgIH07XG5cbiAgICAgICAgICAvLyBDaGVjayBpZiB0aGUgaXRlbSBhbHJlYWR5IGV4aXN0cyBpbiB0aGUgY2FydFxuICAgICAgICAgIGNvbnN0IGV4aXN0aW5nSXRlbUluZGV4ID0gaXRlbXMuZmluZEluZGV4KFxuICAgICAgICAgICAgKGNhcnRJdGVtKSA9PlxuICAgICAgICAgICAgICBjYXJ0SXRlbS5wcm9kdWN0SWQgPT09IG5vcm1hbGl6ZWRJdGVtLnByb2R1Y3RJZCAmJlxuICAgICAgICAgICAgICBjYXJ0SXRlbS52YXJpYXRpb25JZCA9PT0gbm9ybWFsaXplZEl0ZW0udmFyaWF0aW9uSWRcbiAgICAgICAgICApO1xuXG4gICAgICAgICAgaWYgKGV4aXN0aW5nSXRlbUluZGV4ICE9PSAtMSkge1xuICAgICAgICAgICAgLy8gSWYgaXRlbSBleGlzdHMsIHVwZGF0ZSBxdWFudGl0eVxuICAgICAgICAgICAgY29uc3QgdXBkYXRlZEl0ZW1zID0gWy4uLml0ZW1zXTtcbiAgICAgICAgICAgIHVwZGF0ZWRJdGVtc1tleGlzdGluZ0l0ZW1JbmRleF0ucXVhbnRpdHkgKz0gbm9ybWFsaXplZEl0ZW0ucXVhbnRpdHk7XG5cbiAgICAgICAgICAgIHNldCh7XG4gICAgICAgICAgICAgIGl0ZW1zOiB1cGRhdGVkSXRlbXMsXG4gICAgICAgICAgICAgIGl0ZW1Db3VudDogdXBkYXRlZEl0ZW1zLnJlZHVjZSgoc3VtLCBpdGVtKSA9PiBzdW0gKyBpdGVtLnF1YW50aXR5LCAwKSxcbiAgICAgICAgICAgICAgaXNMb2FkaW5nOiBmYWxzZSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAvLyBJZiBpdGVtIGRvZXNuJ3QgZXhpc3QsIGFkZCBpdCB3aXRoIGEgbmV3IElEXG4gICAgICAgICAgICBjb25zdCBuZXdJdGVtID0ge1xuICAgICAgICAgICAgICAuLi5ub3JtYWxpemVkSXRlbSxcbiAgICAgICAgICAgICAgaWQ6IGdlbmVyYXRlSXRlbUlkKCksXG4gICAgICAgICAgICB9O1xuXG4gICAgICAgICAgICBzZXQoe1xuICAgICAgICAgICAgICBpdGVtczogWy4uLml0ZW1zLCBuZXdJdGVtXSxcbiAgICAgICAgICAgICAgaXRlbUNvdW50OiBpdGVtcy5yZWR1Y2UoKHN1bSwgaXRlbSkgPT4gc3VtICsgaXRlbS5xdWFudGl0eSwgMCkgKyBuZXdJdGVtLnF1YW50aXR5LFxuICAgICAgICAgICAgICBpc0xvYWRpbmc6IGZhbHNlLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8gU2hvdyBzdWNjZXNzIG1lc3NhZ2VcbiAgICAgICAgICBjb25zb2xlLmxvZygnSXRlbSBhZGRlZCB0byBjYXJ0IHN1Y2Nlc3NmdWxseScpO1xuXG4gICAgICAgICAgLy8gU3RvcmUgdGhlIHVwZGF0ZWQgY2FydCBpbiBsb2NhbFN0b3JhZ2UgaW1tZWRpYXRlbHkgdG8gcHJldmVudCBsb3NzXG4gICAgICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICBjb25zdCBzdGF0ZSA9IHtcbiAgICAgICAgICAgICAgICBzdGF0ZToge1xuICAgICAgICAgICAgICAgICAgaXRlbXM6IGdldCgpLml0ZW1zLFxuICAgICAgICAgICAgICAgICAgaXRlbUNvdW50OiBnZXQoKS5pdGVtQ291bnQsXG4gICAgICAgICAgICAgICAgICBpc0xvYWRpbmc6IGZhbHNlLFxuICAgICAgICAgICAgICAgICAgZXJyb3I6IG51bGxcbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIHZlcnNpb246IFNUT1JBR0VfVkVSU0lPTlxuICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnYW5ra29yLWxvY2FsLWNhcnQnLCBKU09OLnN0cmluZ2lmeShzdGF0ZSkpO1xuICAgICAgICAgICAgfSBjYXRjaCAoc3RvcmFnZUVycm9yKSB7XG4gICAgICAgICAgICAgIGNvbnNvbGUud2FybignRmFpbGVkIHRvIG1hbnVhbGx5IHBlcnNpc3QgY2FydCB0byBsb2NhbFN0b3JhZ2U6Jywgc3RvcmFnZUVycm9yKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYWRkaW5nIGl0ZW0gdG8gY2FydDonLCBlcnJvcik7XG4gICAgICAgICAgc2V0KHtcbiAgICAgICAgICAgIGVycm9yOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdBbiB1bmtub3duIGVycm9yIG9jY3VycmVkJyxcbiAgICAgICAgICAgIGlzTG9hZGluZzogZmFsc2UsXG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH0sXG5cbiAgICAgIHVwZGF0ZUNhcnRJdGVtOiAoaWQsIHF1YW50aXR5KSA9PiB7XG4gICAgICAgIHNldCh7IGlzTG9hZGluZzogdHJ1ZSwgZXJyb3I6IG51bGwgfSk7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgY29uc3QgaXRlbXMgPSBnZXQoKS5pdGVtcztcbiAgICAgICAgICBpZiAocXVhbnRpdHkgPD0gMCkge1xuICAgICAgICAgICAgLy8gSWYgcXVhbnRpdHkgaXMgMCBvciBuZWdhdGl2ZSwgcmVtb3ZlIHRoZSBpdGVtXG4gICAgICAgICAgICByZXR1cm4gZ2V0KCkucmVtb3ZlQ2FydEl0ZW0oaWQpO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIC8vIEZpbmQgdGhlIGl0ZW0gYW5kIHVwZGF0ZSBpdHMgcXVhbnRpdHlcbiAgICAgICAgICBjb25zdCB1cGRhdGVkSXRlbXMgPSBpdGVtcy5tYXAoaXRlbSA9PlxuICAgICAgICAgICAgaXRlbS5pZCA9PT0gaWQgPyB7IC4uLml0ZW0sIHF1YW50aXR5IH0gOiBpdGVtXG4gICAgICAgICAgKTtcblxuICAgICAgICAgIHNldCh7XG4gICAgICAgICAgICBpdGVtczogdXBkYXRlZEl0ZW1zLFxuICAgICAgICAgICAgaXRlbUNvdW50OiB1cGRhdGVkSXRlbXMucmVkdWNlKChzdW0sIGl0ZW0pID0+IHN1bSArIGl0ZW0ucXVhbnRpdHksIDApLFxuICAgICAgICAgICAgaXNMb2FkaW5nOiBmYWxzZSxcbiAgICAgICAgICB9KTtcblxuICAgICAgICAgIC8vIEltbWVkaWF0ZWx5IHBlcnNpc3QgdG8gbG9jYWxTdG9yYWdlXG4gICAgICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICBjb25zdCBzdGF0ZSA9IHtcbiAgICAgICAgICAgICAgICBzdGF0ZToge1xuICAgICAgICAgICAgICAgICAgaXRlbXM6IHVwZGF0ZWRJdGVtcyxcbiAgICAgICAgICAgICAgICAgIGl0ZW1Db3VudDogdXBkYXRlZEl0ZW1zLnJlZHVjZSgoc3VtLCBpdGVtKSA9PiBzdW0gKyBpdGVtLnF1YW50aXR5LCAwKSxcbiAgICAgICAgICAgICAgICAgIGlzTG9hZGluZzogZmFsc2UsXG4gICAgICAgICAgICAgICAgICBlcnJvcjogbnVsbFxuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgdmVyc2lvbjogU1RPUkFHRV9WRVJTSU9OXG4gICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdhbmtrb3ItbG9jYWwtY2FydCcsIEpTT04uc3RyaW5naWZ5KHN0YXRlKSk7XG4gICAgICAgICAgICB9IGNhdGNoIChzdG9yYWdlRXJyb3IpIHtcbiAgICAgICAgICAgICAgY29uc29sZS53YXJuKCdGYWlsZWQgdG8gbWFudWFsbHkgcGVyc2lzdCBjYXJ0IHVwZGF0ZSB0byBsb2NhbFN0b3JhZ2U6Jywgc3RvcmFnZUVycm9yKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBkYXRpbmcgY2FydCBpdGVtOicsIGVycm9yKTtcbiAgICAgICAgICBzZXQoe1xuICAgICAgICAgICAgZXJyb3I6IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ0FuIHVua25vd24gZXJyb3Igb2NjdXJyZWQnLFxuICAgICAgICAgICAgaXNMb2FkaW5nOiBmYWxzZSxcbiAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgfSxcblxuICAgICAgcmVtb3ZlQ2FydEl0ZW06IChpZCkgPT4ge1xuICAgICAgICBzZXQoeyBpc0xvYWRpbmc6IHRydWUsIGVycm9yOiBudWxsIH0pO1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnN0IGl0ZW1zID0gZ2V0KCkuaXRlbXM7XG4gICAgICAgICAgY29uc3QgdXBkYXRlZEl0ZW1zID0gaXRlbXMuZmlsdGVyKGl0ZW0gPT4gaXRlbS5pZCAhPT0gaWQpO1xuXG4gICAgICAgICAgc2V0KHtcbiAgICAgICAgICAgIGl0ZW1zOiB1cGRhdGVkSXRlbXMsXG4gICAgICAgICAgICBpdGVtQ291bnQ6IHVwZGF0ZWRJdGVtcy5yZWR1Y2UoKHN1bSwgaXRlbSkgPT4gc3VtICsgaXRlbS5xdWFudGl0eSwgMCksXG4gICAgICAgICAgICBpc0xvYWRpbmc6IGZhbHNlLFxuICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgLy8gSW1tZWRpYXRlbHkgcGVyc2lzdCB0byBsb2NhbFN0b3JhZ2VcbiAgICAgICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgIGNvbnN0IHN0YXRlID0ge1xuICAgICAgICAgICAgICAgIHN0YXRlOiB7XG4gICAgICAgICAgICAgICAgICBpdGVtczogdXBkYXRlZEl0ZW1zLFxuICAgICAgICAgICAgICAgICAgaXRlbUNvdW50OiB1cGRhdGVkSXRlbXMucmVkdWNlKChzdW0sIGl0ZW0pID0+IHN1bSArIGl0ZW0ucXVhbnRpdHksIDApLFxuICAgICAgICAgICAgICAgICAgaXNMb2FkaW5nOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgIGVycm9yOiBudWxsXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICB2ZXJzaW9uOiBTVE9SQUdFX1ZFUlNJT05cbiAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2Fua2tvci1sb2NhbC1jYXJ0JywgSlNPTi5zdHJpbmdpZnkoc3RhdGUpKTtcbiAgICAgICAgICAgIH0gY2F0Y2ggKHN0b3JhZ2VFcnJvcikge1xuICAgICAgICAgICAgICBjb25zb2xlLndhcm4oJ0ZhaWxlZCB0byBtYW51YWxseSBwZXJzaXN0IGNhcnQgcmVtb3ZhbCB0byBsb2NhbFN0b3JhZ2U6Jywgc3RvcmFnZUVycm9yKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgcmVtb3ZpbmcgY2FydCBpdGVtOicsIGVycm9yKTtcbiAgICAgICAgICBzZXQoe1xuICAgICAgICAgICAgZXJyb3I6IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ0FuIHVua25vd24gZXJyb3Igb2NjdXJyZWQnLFxuICAgICAgICAgICAgaXNMb2FkaW5nOiBmYWxzZSxcbiAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgfSxcblxuICAgICAgY2xlYXJDYXJ0OiAoKSA9PiB7XG4gICAgICAgIHNldCh7XG4gICAgICAgICAgaXRlbXM6IFtdLFxuICAgICAgICAgIGl0ZW1Db3VudDogMCxcbiAgICAgICAgICBpc0xvYWRpbmc6IGZhbHNlLFxuICAgICAgICAgIGVycm9yOiBudWxsLFxuICAgICAgICB9KTtcblxuICAgICAgICAvLyBJbW1lZGlhdGVseSBwZXJzaXN0IHRvIGxvY2FsU3RvcmFnZVxuICAgICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc3Qgc3RhdGUgPSB7XG4gICAgICAgICAgICAgIHN0YXRlOiB7XG4gICAgICAgICAgICAgICAgaXRlbXM6IFtdLFxuICAgICAgICAgICAgICAgIGl0ZW1Db3VudDogMCxcbiAgICAgICAgICAgICAgICBpc0xvYWRpbmc6IGZhbHNlLFxuICAgICAgICAgICAgICAgIGVycm9yOiBudWxsXG4gICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgIHZlcnNpb246IFNUT1JBR0VfVkVSU0lPTlxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdhbmtrb3ItbG9jYWwtY2FydCcsIEpTT04uc3RyaW5naWZ5KHN0YXRlKSk7XG4gICAgICAgICAgfSBjYXRjaCAoc3RvcmFnZUVycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLndhcm4oJ0ZhaWxlZCB0byBtYW51YWxseSBwZXJzaXN0IGNhcnQgY2xlYXJpbmcgdG8gbG9jYWxTdG9yYWdlOicsIHN0b3JhZ2VFcnJvcik7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9LFxuXG4gICAgICBzZXRFcnJvcjogKGVycm9yKSA9PiB7XG4gICAgICAgIHNldCh7IGVycm9yIH0pO1xuICAgICAgfSxcblxuICAgICAgc2V0SXNMb2FkaW5nOiAoaXNMb2FkaW5nKSA9PiB7XG4gICAgICAgIHNldCh7IGlzTG9hZGluZyB9KTtcbiAgICAgIH0sXG5cbiAgICAgIC8vIEhlbHBlciBtZXRob2RzXG4gICAgICBzdWJ0b3RhbDogKCkgPT4ge1xuICAgICAgICBjb25zdCBpdGVtcyA9IGdldCgpLml0ZW1zO1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnN0IGNhbGN1bGF0ZWRTdWJ0b3RhbCA9IGl0ZW1zLnJlZHVjZSgodG90YWwsIGl0ZW0pID0+IHtcbiAgICAgICAgICAgIC8vIEhhbmRsZSBwcmljZSB3aXRoIG9yIHdpdGhvdXQgY3VycmVuY3kgc3ltYm9sXG4gICAgICAgICAgICBsZXQgaXRlbVByaWNlID0gMDtcbiAgICAgICAgICAgIGlmICh0eXBlb2YgaXRlbS5wcmljZSA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgICAgICAgLy8gUmVtb3ZlIGN1cnJlbmN5IHN5bWJvbCBpZiBwcmVzZW50XG4gICAgICAgICAgICAgIGNvbnN0IHByaWNlU3RyaW5nID0gaXRlbS5wcmljZS5yZXBsYWNlKC9b4oK5JOKCrMKjXS9nLCAnJykudHJpbSgpO1xuICAgICAgICAgICAgICAvLyBSZXBsYWNlIGNvbW1hIHdpdGggZW1wdHkgc3RyaW5nIGlmIHByZXNlbnQgKGZvciBJbmRpYW4gbnVtYmVyIGZvcm1hdClcbiAgICAgICAgICAgICAgY29uc3QgY2xlYW5QcmljZSA9IHByaWNlU3RyaW5nLnJlcGxhY2UoLywvZywgJycpO1xuICAgICAgICAgICAgICBpdGVtUHJpY2UgPSBwYXJzZUZsb2F0KGNsZWFuUHJpY2UpO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgaXRlbVByaWNlID0gaXRlbS5wcmljZTtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgaWYgKGlzTmFOKGl0ZW1QcmljZSkpIHtcbiAgICAgICAgICAgICAgY29uc29sZS53YXJuKGBJbnZhbGlkIHByaWNlIGZvciBpdGVtICR7aXRlbS5pZH06ICR7aXRlbS5wcmljZX1gKTtcbiAgICAgICAgICAgICAgcmV0dXJuIHRvdGFsO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICByZXR1cm4gdG90YWwgKyAoaXRlbVByaWNlICogaXRlbS5xdWFudGl0eSk7XG4gICAgICAgICAgfSwgMCk7XG5cbiAgICAgICAgICByZXR1cm4gaXNOYU4oY2FsY3VsYXRlZFN1YnRvdGFsKSA/IDAgOiBjYWxjdWxhdGVkU3VidG90YWw7XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY2FsY3VsYXRpbmcgc3VidG90YWw6JywgZXJyb3IpO1xuICAgICAgICAgIHJldHVybiAwO1xuICAgICAgICB9XG4gICAgICB9LFxuXG4gICAgICB0b3RhbDogKCkgPT4ge1xuICAgICAgICAvLyBGb3Igbm93LCB0b3RhbCBpcyB0aGUgc2FtZSBhcyBzdWJ0b3RhbFxuICAgICAgICAvLyBJbiB0aGUgZnV0dXJlLCB5b3UgY291bGQgYWRkIHNoaXBwaW5nLCB0YXgsIGV0Yy5cbiAgICAgICAgY29uc3QgY2FsY3VsYXRlZFRvdGFsID0gZ2V0KCkuc3VidG90YWwoKTtcbiAgICAgICAgcmV0dXJuIGlzTmFOKGNhbGN1bGF0ZWRUb3RhbCkgPyAwIDogY2FsY3VsYXRlZFRvdGFsO1xuICAgICAgfSxcblxuICAgICAgLy8gU3luYyBjYXJ0IHdpdGggV29vQ29tbWVyY2UgdXNpbmcgU3RvcmUgQVBJXG4gICAgICBzeW5jV2l0aFdvb0NvbW1lcmNlOiBhc3luYyAoYXV0aFRva2VuPzogc3RyaW5nIHwgbnVsbCkgPT4ge1xuICAgICAgICBjb25zdCB7IGl0ZW1zIH0gPSBnZXQoKTtcbiAgICAgICAgaWYgKGl0ZW1zLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcignQ2FydCBpcyBlbXB0eScpO1xuICAgICAgICB9XG5cbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBjb25zb2xlLmxvZygnU3luY2luZyBjYXJ0IHdpdGggV29vQ29tbWVyY2UuLi4nKTtcbiAgICAgICAgICBjb25zb2xlLmxvZygnQXV0aCB0b2tlbiBwcm92aWRlZDonLCAhIWF1dGhUb2tlbik7XG4gICAgICAgICAgc2V0KHsgaXNMb2FkaW5nOiB0cnVlIH0pO1xuXG4gICAgICAgICAgLy8gSWYgdXNlciBpcyBsb2dnZWQgaW4sIHVzZSB0aGUgSldULXRvLUNvb2tpZSBicmlkZ2UgZm9yIHNlYW1sZXNzIGNoZWNrb3V0XG4gICAgICAgICAgaWYgKGF1dGhUb2tlbikge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ1VzZXIgaXMgYXV0aGVudGljYXRlZCwgdXNpbmcgSldULXRvLUNvb2tpZSBicmlkZ2UnKTtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgIGNvbnN0IGNoZWNrb3V0VXJsID0gYXdhaXQgY3JlYXRlV3BTZXNzaW9uQW5kR2V0Q2hlY2tvdXRVcmwoYXV0aFRva2VuLCBpdGVtcyk7XG4gICAgICAgICAgICAgIHNldCh7IGlzTG9hZGluZzogZmFsc2UgfSk7XG4gICAgICAgICAgICAgIHJldHVybiBjaGVja291dFVybDtcbiAgICAgICAgICAgIH0gY2F0Y2ggKGJyaWRnZUVycm9yKSB7XG4gICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0pXVC10by1Db29raWUgYnJpZGdlIGZhaWxlZDonLCBicmlkZ2VFcnJvcik7XG4gICAgICAgICAgICAgIC8vIEZhbGwgYmFjayB0byBndWVzdCBjaGVja291dCBpZiB0aGUgYnJpZGdlIGZhaWxzXG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdGYWxsaW5nIGJhY2sgdG8gZ3Vlc3QgY2hlY2tvdXQuLi4nKTtcbiAgICAgICAgICAgICAgLy8gQ29udGludWUgd2l0aCBndWVzdCBjaGVja291dCBmbG93IGJlbG93XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8gRm9yIGd1ZXN0IHVzZXJzLCByZWRpcmVjdCBkaXJlY3RseSB0byBXb29Db21tZXJjZSBjaGVja291dFxuICAgICAgICAgIGNvbnNvbGUubG9nKCdVc2VyIGlzIG5vdCBhdXRoZW50aWNhdGVkLCByZWRpcmVjdGluZyB0byBXb29Db21tZXJjZSBjaGVja291dCcpO1xuICAgICAgICAgIGNvbnN0IGJhc2VVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19XT09DT01NRVJDRV9VUkwgfHwgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfV09SRFBSRVNTX1VSTDtcbiAgICAgICAgICBjb25zdCBjaGVja291dFVybCA9IGAke2Jhc2VVcmx9L2NoZWNrb3V0L2A7XG4gICAgICAgICAgY29uc29sZS5sb2coJ0d1ZXN0IGNoZWNrb3V0IFVSTDonLCBjaGVja291dFVybCk7XG4gICAgICAgICAgc2V0KHsgaXNMb2FkaW5nOiBmYWxzZSB9KTtcbiAgICAgICAgICByZXR1cm4gY2hlY2tvdXRVcmw7XG4gICAgICAgICAgXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc3luY2luZyBjYXJ0IHdpdGggV29vQ29tbWVyY2U6JywgZXJyb3IpO1xuICAgICAgICAgIHNldCh7IGlzTG9hZGluZzogZmFsc2UgfSk7XG4gICAgICAgICAgXG4gICAgICAgICAgLy8gRmFsbGJhY2sgYXBwcm9hY2g6IHVzZSBVUkwgcGFyYW1ldGVycyB0byBidWlsZCBjYXJ0XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdBdHRlbXB0aW5nIGZhbGxiYWNrIG1ldGhvZCBmb3IgY2FydCBzeW5jLi4uJyk7XG4gICAgICAgICAgICBjb25zdCBiYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfV09PQ09NTUVSQ0VfVVJMIHx8IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1dPUkRQUkVTU19VUkw7XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC8vIEJ1aWxkIFVSTCB3aXRoIGFkZC10by1jYXJ0IHBhcmFtZXRlcnMgZm9yIGVhY2ggaXRlbVxuICAgICAgICAgICAgbGV0IGNoZWNrb3V0VXJsID0gYCR7YmFzZVVybH0vY2hlY2tvdXQvP2d1ZXN0X2NoZWNrb3V0PXllcyZjaGVja291dF93b29jb21tZXJjZV9jaGVja291dF9sb2dpbl9yZW1pbmRlcj0wJmNyZWF0ZV9hY2NvdW50PTAmc2tpcF9sb2dpbj0xJmZvcmNlX2d1ZXN0X2NoZWNrb3V0PTFgO1xuICAgICAgICAgICAgXG4gICAgICAgICAgICAvLyBBZGQgZWFjaCBpdGVtIGFzIGEgVVJMIHBhcmFtZXRlclxuICAgICAgICAgICAgaXRlbXMuZm9yRWFjaCgoaXRlbSwgaW5kZXgpID0+IHtcbiAgICAgICAgICAgICAgaWYgKGluZGV4ID09PSAwKSB7XG4gICAgICAgICAgICAgICAgY2hlY2tvdXRVcmwgKz0gYCZhZGQtdG8tY2FydD0ke2l0ZW0ucHJvZHVjdElkfSZxdWFudGl0eT0ke2l0ZW0ucXVhbnRpdHl9YDtcbiAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAvLyBGb3IgV29vQ29tbWVyY2UsIGFkZGl0aW9uYWwgaXRlbXMgbmVlZCBhIGRpZmZlcmVudCBmb3JtYXRcbiAgICAgICAgICAgICAgICBjaGVja291dFVybCArPSBgJmFkZC10by1jYXJ0WyR7aW5kZXh9XT0ke2l0ZW0ucHJvZHVjdElkfSZxdWFudGl0eVske2luZGV4fV09JHtpdGVtLnF1YW50aXR5fWA7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIC8vIEFkZCB2YXJpYXRpb24gSUQgaWYgcHJlc2VudFxuICAgICAgICAgICAgICBpZiAoaXRlbS52YXJpYXRpb25JZCkge1xuICAgICAgICAgICAgICAgIGNoZWNrb3V0VXJsICs9IGAmdmFyaWF0aW9uX2lkPSR7aXRlbS52YXJpYXRpb25JZH1gO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgY29uc29sZS5sb2coJ0ZhbGxiYWNrIGNoZWNrb3V0IFVSTDonLCBjaGVja291dFVybCk7XG4gICAgICAgICAgICByZXR1cm4gY2hlY2tvdXRVcmw7XG4gICAgICAgICAgfSBjYXRjaCAoZmFsbGJhY2tFcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcignRmFsbGJhY2sgbWV0aG9kIGZhaWxlZDonLCBmYWxsYmFja0Vycm9yKTtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIHN5bmMgY2FydCB3aXRoIFdvb0NvbW1lcmNlLiBQbGVhc2UgdHJ5IGFnYWluIG9yIGNvbnRhY3Qgc3VwcG9ydC4nKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgfSksXG4gICAge1xuICAgICAgbmFtZTogJ2Fua2tvci1sb2NhbC1jYXJ0JyxcbiAgICAgIHZlcnNpb246IFNUT1JBR0VfVkVSU0lPTixcbiAgICB9XG4gIClcbik7XG5cbi8vIEhlbHBlciBob29rc1xuZXhwb3J0IGNvbnN0IHVzZUxvY2FsQ2FydEl0ZW1zID0gKCkgPT4gdXNlTG9jYWxDYXJ0U3RvcmUoc3RhdGUgPT4gc3RhdGUuaXRlbXMpO1xuZXhwb3J0IGNvbnN0IHVzZUxvY2FsQ2FydENvdW50ID0gKCkgPT4gdXNlTG9jYWxDYXJ0U3RvcmUoc3RhdGUgPT4gc3RhdGUuaXRlbUNvdW50KTtcbmV4cG9ydCBjb25zdCB1c2VMb2NhbENhcnRTdWJ0b3RhbCA9ICgpID0+IHVzZUxvY2FsQ2FydFN0b3JlKHN0YXRlID0+IHN0YXRlLnN1YnRvdGFsKCkpO1xuZXhwb3J0IGNvbnN0IHVzZUxvY2FsQ2FydFRvdGFsID0gKCkgPT4gdXNlTG9jYWxDYXJ0U3RvcmUoc3RhdGUgPT4gc3RhdGUudG90YWwoKSk7XG5leHBvcnQgY29uc3QgdXNlTG9jYWxDYXJ0TG9hZGluZyA9ICgpID0+IHVzZUxvY2FsQ2FydFN0b3JlKHN0YXRlID0+IHN0YXRlLmlzTG9hZGluZyk7XG5leHBvcnQgY29uc3QgdXNlTG9jYWxDYXJ0RXJyb3IgPSAoKSA9PiB1c2VMb2NhbENhcnRTdG9yZShzdGF0ZSA9PiBzdGF0ZS5lcnJvcik7XG5cbi8vIEhlbHBlciBmdW5jdGlvbnNcbmV4cG9ydCBjb25zdCBmb3JtYXRQcmljZSA9IChwcmljZTogc3RyaW5nIHwgbnVtYmVyLCBjdXJyZW5jeUNvZGUgPSAnSU5SJykgPT4ge1xuICBjb25zdCBhbW91bnQgPSB0eXBlb2YgcHJpY2UgPT09ICdzdHJpbmcnID8gcGFyc2VGbG9hdChwcmljZSkgOiBwcmljZTtcbiAgcmV0dXJuIG5ldyBJbnRsLk51bWJlckZvcm1hdCgnZW4tSU4nLCB7XG4gICAgc3R5bGU6ICdjdXJyZW5jeScsXG4gICAgY3VycmVuY3k6IGN1cnJlbmN5Q29kZSxcbiAgICBtaW5pbXVtRnJhY3Rpb25EaWdpdHM6IDIsXG4gICAgbWF4aW11bUZyYWN0aW9uRGlnaXRzOiAyLFxuICB9KS5mb3JtYXQoYW1vdW50KTtcbn07XG5cbi8vIENsZWFyIGNhcnQgYWZ0ZXIgc3VjY2Vzc2Z1bCBjaGVja291dFxuZXhwb3J0IGNvbnN0IGNsZWFyQ2FydEFmdGVyQ2hlY2tvdXQgPSAoKSA9PiB7XG4gIHVzZUxvY2FsQ2FydFN0b3JlLmdldFN0YXRlKCkuY2xlYXJDYXJ0KCk7XG4gIFxuICAvLyBBbHNvIHJlc2V0IHRoZSBjYXJ0IHRva2VuIHRvIGVuc3VyZSBhIGZyZXNoIGNhcnQgZm9yIHRoZSBuZXh0IHNlc3Npb25cbiAgLy8gY2FydFNlc3Npb24ucmVzZXRDYXJ0VG9rZW4oKTsgLy8gVGhpcyBsaW5lIHdhcyByZW1vdmVkIGFzIHBlciB0aGUgZWRpdCBoaW50XG59O1xuXG5cblxuLyoqXG4gKiBDcmVhdGUgV29yZFByZXNzIHNlc3Npb24gZnJvbSBKV1QgdG9rZW4gYW5kIGdldCB0aGUgY2hlY2tvdXQgVVJMXG4gKiBUaGlzIGltcGxlbWVudHMgdGhlIEpXVC10by1Db29raWUgQnJpZGdlIGZvciBzZWFtbGVzcyBjaGVja291dCBleHBlcmllbmNlXG4gKiBAcGFyYW0gYXV0aFRva2VuIFRoZSBKV1QgYXV0aGVudGljYXRpb24gdG9rZW5cbiAqIEBwYXJhbSBpdGVtcyBDYXJ0IGl0ZW1zIHRvIGluY2x1ZGUgaW4gY2hlY2tvdXRcbiAqIEByZXR1cm5zIFRoZSBXb29Db21tZXJjZSBjaGVja291dCBVUkxcbiAqL1xuYXN5bmMgZnVuY3Rpb24gY3JlYXRlV3BTZXNzaW9uQW5kR2V0Q2hlY2tvdXRVcmwoYXV0aFRva2VuOiBzdHJpbmcsIGl0ZW1zOiBDYXJ0SXRlbVtdKTogUHJvbWlzZTxzdHJpbmc+IHtcbiAgaWYgKCFhdXRoVG9rZW4pIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ0F1dGhlbnRpY2F0aW9uIHRva2VuIGlzIHJlcXVpcmVkJyk7XG4gIH1cblxuICBjb25zdCB3cFVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1dQX1VSTDtcbiAgY29uc3QgY2hlY2tvdXRVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19XUF9DSEVDS09VVF9VUkw7XG5cbiAgaWYgKCF3cFVybCB8fCAhY2hlY2tvdXRVcmwpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ1dvcmRQcmVzcyBvciBjaGVja291dCBVUkwgbm90IGNvbmZpZ3VyZWQuIENoZWNrIHlvdXIgZW52aXJvbm1lbnQgdmFyaWFibGVzLicpO1xuICB9XG5cbiAgdHJ5IHtcbiAgICBjb25zb2xlLmxvZygnQ3JlYXRpbmcgV29yZFByZXNzIHNlc3Npb24gZnJvbSBKV1QgdG9rZW4uLi4nKTtcbiAgICBjb25zb2xlLmxvZygnVXNpbmcgZW5kcG9pbnQ6JywgYCR7d3BVcmx9L3dwLWpzb24vaGVhZGxlc3MvdjEvY3JlYXRlLXdwLXNlc3Npb25gKTtcbiAgICBjb25zb2xlLmxvZygnVG9rZW4gbGVuZ3RoOicsIGF1dGhUb2tlbi5sZW5ndGgpO1xuICAgIGNvbnNvbGUubG9nKCdUb2tlbiBwcmV2aWV3OicsIGF1dGhUb2tlbi5zdWJzdHJpbmcoMCwgMjApICsgJy4uLicpO1xuXG4gICAgLy8gQ2FsbCB0aGUgY3VzdG9tIFdvcmRQcmVzcyBlbmRwb2ludCB0byBjcmVhdGUgYSBzZXNzaW9uIGZyb20gSldUXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHt3cFVybH0vd3AtanNvbi9oZWFkbGVzcy92MS9jcmVhdGUtd3Atc2Vzc2lvbmAsIHtcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHthdXRoVG9rZW59YFxuICAgICAgfSxcbiAgICAgIC8vIFRISVMgSVMgVEhFIENSSVRJQ0FMIExJTkUgLSBJbmNsdWRlIHRva2VuIGluIHJlcXVlc3QgYm9keSBhcyB3ZWxsXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7IHRva2VuOiBhdXRoVG9rZW4gfSksXG4gICAgICBjcmVkZW50aWFsczogJ2luY2x1ZGUnLCAvLyBDcml0aWNhbDogVGhpcyBhbGxvd3MgdGhlIGJyb3dzZXIgdG8gcmVjZWl2ZSBhbmQgc2V0IHRoZSBjb29raWVcbiAgICB9KTtcblxuICAgIGNvbnNvbGUubG9nKCdSZXNwb25zZSBzdGF0dXM6JywgcmVzcG9uc2Uuc3RhdHVzKTtcbiAgICBjb25zb2xlLmxvZygnUmVzcG9uc2UgaGVhZGVyczonLCBPYmplY3QuZnJvbUVudHJpZXMocmVzcG9uc2UuaGVhZGVycy5lbnRyaWVzKCkpKTtcblxuICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgIGxldCBlcnJvck1lc3NhZ2UgPSBgSFRUUCAke3Jlc3BvbnNlLnN0YXR1c306ICR7cmVzcG9uc2Uuc3RhdHVzVGV4dH1gO1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICBlcnJvck1lc3NhZ2UgPSBlcnJvckRhdGEubWVzc2FnZSB8fCBlcnJvckRhdGEuY29kZSB8fCBlcnJvck1lc3NhZ2U7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHJlc3BvbnNlIGRhdGE6JywgZXJyb3JEYXRhKTtcbiAgICAgIH0gY2F0Y2ggKHBhcnNlRXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignQ291bGQgbm90IHBhcnNlIGVycm9yIHJlc3BvbnNlOicsIHBhcnNlRXJyb3IpO1xuICAgICAgfVxuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gY3JlYXRlIFdvcmRQcmVzcyBzZXNzaW9uOiAke2Vycm9yTWVzc2FnZX1gKTtcbiAgICB9XG5cbiAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgIGNvbnNvbGUubG9nKCdSZXNwb25zZSBkYXRhOicsIGRhdGEpO1xuXG4gICAgaWYgKCFkYXRhLnN1Y2Nlc3MpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihkYXRhLm1lc3NhZ2UgfHwgJ0ZhaWxlZCB0byBjcmVhdGUgV29yZFByZXNzIHNlc3Npb24nKTtcbiAgICB9XG5cbiAgICBjb25zb2xlLmxvZygnV29yZFByZXNzIHNlc3Npb24gY3JlYXRlZCBzdWNjZXNzZnVsbHknKTtcbiAgICBjb25zb2xlLmxvZygnUmVkaXJlY3RpbmcgdG8gY2hlY2tvdXQgVVJMOicsIGNoZWNrb3V0VXJsKTtcblxuICAgIC8vIEZvciBhdXRoZW50aWNhdGVkIHVzZXJzLCB3ZSBjYW4gZGlyZWN0bHkgZ28gdG8gY2hlY2tvdXRcbiAgICAvLyBUaGUgc2VydmVyIGFscmVhZHkgaGFzIHRoZSB1c2VyJ3Mgc2Vzc2lvbiBhbmQgd2lsbCBsb2FkIHRoZSBjb3JyZWN0IGNhcnRcbiAgICByZXR1cm4gY2hlY2tvdXRVcmw7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgY3JlYXRpbmcgV29yZFByZXNzIHNlc3Npb246JywgZXJyb3IpO1xuXG4gICAgLy8gUHJvdmlkZSBtb3JlIHNwZWNpZmljIGVycm9yIG1lc3NhZ2VzXG4gICAgaWYgKGVycm9yIGluc3RhbmNlb2YgVHlwZUVycm9yICYmIGVycm9yLm1lc3NhZ2UuaW5jbHVkZXMoJ2ZldGNoJykpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignTmV0d29yayBlcnJvcjogQ291bGQgbm90IGNvbm5lY3QgdG8gV29yZFByZXNzLiBQbGVhc2UgY2hlY2sgeW91ciBpbnRlcm5ldCBjb25uZWN0aW9uLicpO1xuICAgIH1cblxuICAgIHRocm93IG5ldyBFcnJvcihlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdGYWlsZWQgdG8gcHJlcGFyZSBjaGVja291dCcpO1xuICB9XG59XG4iXSwibmFtZXMiOlsiY3JlYXRlIiwicGVyc2lzdCIsIlNUT1JBR0VfVkVSU0lPTiIsImdlbmVyYXRlSXRlbUlkIiwiTWF0aCIsInJhbmRvbSIsInRvU3RyaW5nIiwic3Vic3RyaW5nIiwidXNlTG9jYWxDYXJ0U3RvcmUiLCJzZXQiLCJnZXQiLCJpdGVtcyIsIml0ZW1Db3VudCIsImlzTG9hZGluZyIsImVycm9yIiwiYWRkVG9DYXJ0IiwiaXRlbSIsIm5vcm1hbGl6ZWRQcmljZSIsInByaWNlIiwicHJpY2VTdHJpbmciLCJyZXBsYWNlIiwidHJpbSIsIm5vcm1hbGl6ZWRJdGVtIiwiZXhpc3RpbmdJdGVtSW5kZXgiLCJmaW5kSW5kZXgiLCJjYXJ0SXRlbSIsInByb2R1Y3RJZCIsInZhcmlhdGlvbklkIiwidXBkYXRlZEl0ZW1zIiwicXVhbnRpdHkiLCJyZWR1Y2UiLCJzdW0iLCJuZXdJdGVtIiwiaWQiLCJjb25zb2xlIiwibG9nIiwic3RhdGUiLCJ2ZXJzaW9uIiwibG9jYWxTdG9yYWdlIiwic2V0SXRlbSIsIkpTT04iLCJzdHJpbmdpZnkiLCJzdG9yYWdlRXJyb3IiLCJ3YXJuIiwiRXJyb3IiLCJtZXNzYWdlIiwidXBkYXRlQ2FydEl0ZW0iLCJyZW1vdmVDYXJ0SXRlbSIsIm1hcCIsImZpbHRlciIsImNsZWFyQ2FydCIsInNldEVycm9yIiwic2V0SXNMb2FkaW5nIiwic3VidG90YWwiLCJjYWxjdWxhdGVkU3VidG90YWwiLCJ0b3RhbCIsIml0ZW1QcmljZSIsImNsZWFuUHJpY2UiLCJwYXJzZUZsb2F0IiwiaXNOYU4iLCJjYWxjdWxhdGVkVG90YWwiLCJzeW5jV2l0aFdvb0NvbW1lcmNlIiwiYXV0aFRva2VuIiwibGVuZ3RoIiwiY2hlY2tvdXRVcmwiLCJjcmVhdGVXcFNlc3Npb25BbmRHZXRDaGVja291dFVybCIsImJyaWRnZUVycm9yIiwiYmFzZVVybCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19XT09DT01NRVJDRV9VUkwiLCJORVhUX1BVQkxJQ19XT1JEUFJFU1NfVVJMIiwiZm9yRWFjaCIsImluZGV4IiwiZmFsbGJhY2tFcnJvciIsIm5hbWUiLCJ1c2VMb2NhbENhcnRJdGVtcyIsInVzZUxvY2FsQ2FydENvdW50IiwidXNlTG9jYWxDYXJ0U3VidG90YWwiLCJ1c2VMb2NhbENhcnRUb3RhbCIsInVzZUxvY2FsQ2FydExvYWRpbmciLCJ1c2VMb2NhbENhcnRFcnJvciIsImZvcm1hdFByaWNlIiwiY3VycmVuY3lDb2RlIiwiYW1vdW50IiwiSW50bCIsIk51bWJlckZvcm1hdCIsInN0eWxlIiwiY3VycmVuY3kiLCJtaW5pbXVtRnJhY3Rpb25EaWdpdHMiLCJtYXhpbXVtRnJhY3Rpb25EaWdpdHMiLCJmb3JtYXQiLCJjbGVhckNhcnRBZnRlckNoZWNrb3V0IiwiZ2V0U3RhdGUiLCJ3cFVybCIsIk5FWFRfUFVCTElDX1dQX1VSTCIsIk5FWFRfUFVCTElDX1dQX0NIRUNLT1VUX1VSTCIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsInRva2VuIiwiY3JlZGVudGlhbHMiLCJzdGF0dXMiLCJPYmplY3QiLCJmcm9tRW50cmllcyIsImVudHJpZXMiLCJvayIsImVycm9yTWVzc2FnZSIsInN0YXR1c1RleHQiLCJlcnJvckRhdGEiLCJqc29uIiwiY29kZSIsInBhcnNlRXJyb3IiLCJkYXRhIiwic3VjY2VzcyIsIlR5cGVFcnJvciIsImluY2x1ZGVzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/localCartStore.ts\n"));

/***/ })

}]);