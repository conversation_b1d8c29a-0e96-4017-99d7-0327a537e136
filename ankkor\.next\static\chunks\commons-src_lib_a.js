"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["commons-src_lib_a"],{

/***/ "(app-pages-browser)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addCustomerAddress: function() { return /* binding */ addCustomerAddress; },\n/* harmony export */   deleteCustomerAddress: function() { return /* binding */ deleteCustomerAddress; },\n/* harmony export */   getCurrentCustomer: function() { return /* binding */ getCurrentCustomer; },\n/* harmony export */   getCustomerSession: function() { return /* binding */ getCustomerSession; },\n/* harmony export */   isCustomerLoggedIn: function() { return /* binding */ isCustomerLoggedIn; },\n/* harmony export */   loginCustomer: function() { return /* binding */ loginCustomer; },\n/* harmony export */   logoutCustomer: function() { return /* binding */ logoutCustomer; },\n/* harmony export */   registerCustomer: function() { return /* binding */ registerCustomer; },\n/* harmony export */   setCustomerDefaultAddress: function() { return /* binding */ setCustomerDefaultAddress; },\n/* harmony export */   updateCustomerAddress: function() { return /* binding */ updateCustomerAddress; },\n/* harmony export */   updateCustomerProfile: function() { return /* binding */ updateCustomerProfile; }\n/* harmony export */ });\n/* harmony import */ var _woocommerce__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./woocommerce */ \"(app-pages-browser)/./src/lib/woocommerce.ts\");\n/**\n * Authentication module that works with the WooCommerce authentication API\n */ \n// Local storage keys\nconst AUTH_TOKEN_KEY = \"woo_auth_token\";\nconst REFRESH_TOKEN_KEY = \"woo_refresh_token\";\n/**\n * Register a new customer with WooCommerce\n * @param registration Customer registration data\n * @returns Customer data and access token if successful\n */ async function registerCustomer(registration) {\n    try {\n        const { email, password, firstName, lastName, phone, acceptsMarketing = false, address } = registration;\n        const result = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.createCustomer)({\n            email,\n            password,\n            firstName,\n            lastName,\n            phone,\n            acceptsMarketing\n        });\n        if (result.customerUserErrors && result.customerUserErrors.length > 0) {\n            throw new Error(result.customerUserErrors[0].message);\n        }\n        if (!result.authToken) {\n            throw new Error(\"Registration failed: No authentication token returned\");\n        }\n        // Store the token in localStorage\n        saveCustomerToken({\n            accessToken: result.authToken,\n            expiresAt: calculateTokenExpiry(result.authToken)\n        });\n        // Save refresh token if available\n        if (result.refreshToken) {\n            localStorage.setItem(REFRESH_TOKEN_KEY, result.refreshToken);\n        }\n        // Get customer data using the new token\n        const customer = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.getCustomer)(result.authToken);\n        return {\n            customer,\n            accessToken: result.authToken,\n            expiresAt: calculateTokenExpiry(result.authToken)\n        };\n    } catch (error) {\n        console.error(\"Error registering customer:\", error);\n        throw error;\n    }\n}\n/**\n * Log in a customer with WooCommerce\n * @param credentials Customer credentials\n * @returns Customer data and access token if successful\n */ async function loginCustomer(credentials) {\n    try {\n        const { email, password } = credentials;\n        const result = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.customerLogin)(email, password);\n        if (result.customerUserErrors && result.customerUserErrors.length > 0) {\n            throw new Error(result.customerUserErrors[0].message);\n        }\n        if (!result.authToken) {\n            throw new Error(\"Login failed: No authentication token returned\");\n        }\n        // Store the token in localStorage\n        saveCustomerToken({\n            accessToken: result.authToken,\n            expiresAt: calculateTokenExpiry(result.authToken)\n        });\n        // Save refresh token if available\n        if (result.refreshToken) {\n            localStorage.setItem(REFRESH_TOKEN_KEY, result.refreshToken);\n        }\n        // Get customer data using the new token\n        const customer = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.getCustomer)(result.authToken);\n        return {\n            customer,\n            accessToken: result.authToken,\n            expiresAt: calculateTokenExpiry(result.authToken)\n        };\n    } catch (error) {\n        console.error(\"Error logging in customer:\", error);\n        throw error;\n    }\n}\n/**\n * Calculate token expiry from JWT token\n * @param token JWT token\n * @returns ISO date string of expiry\n */ function calculateTokenExpiry(token) {\n    try {\n        // Basic JWT parsing - tokens have 3 parts separated by dots\n        const parts = token.split(\".\");\n        if (parts.length !== 3) {\n            // If token isn't valid JWT format, set default expiry to 1 day\n            const date = new Date();\n            date.setDate(date.getDate() + 1);\n            return date.toISOString();\n        }\n        // Decode the payload (middle part)\n        const payload = JSON.parse(atob(parts[1]));\n        // JWT uses 'exp' field for expiry timestamp (in seconds)\n        if (payload.exp) {\n            return new Date(payload.exp * 1000).toISOString();\n        } else {\n            // Fallback: set expiry to 1 day if no exp field\n            const date = new Date();\n            date.setDate(date.getDate() + 1);\n            return date.toISOString();\n        }\n    } catch (error) {\n        console.error(\"Error calculating token expiry:\", error);\n        // Fallback: set expiry to 1 day\n        const date = new Date();\n        date.setDate(date.getDate() + 1);\n        return date.toISOString();\n    }\n}\n/**\n * Update a customer's profile\n * @param customerData Customer data to update\n * @returns Updated customer data\n */ async function updateCustomerProfile(customerData) {\n    try {\n        // Use the API endpoint to update the profile since it has access to HTTP-only cookies\n        const response = await fetch(\"/api/auth/update-profile\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\",\n            body: JSON.stringify(customerData)\n        });\n        const result = await response.json();\n        if (!response.ok || !result.success) {\n            throw new Error(result.message || \"Profile update failed\");\n        }\n        return {\n            customer: result.customer,\n            accessToken: \"token_managed_by_server\",\n            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()\n        };\n    } catch (error) {\n        console.error(\"Error updating customer profile:\", error);\n        throw error;\n    }\n}\n/**\n * Add a new address for the customer\n * @param address Address data\n * @returns Updated customer data\n */ async function addCustomerAddress(address) {\n    try {\n        const session = getCustomerSession();\n        if (!session) {\n            throw new Error(\"No active customer session found\");\n        }\n        const result = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.createAddress)(session.accessToken, address);\n        if (result.customerUserErrors && result.customerUserErrors.length > 0) {\n            throw new Error(result.customerUserErrors[0].message);\n        }\n        // Get updated customer data\n        const customer = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.getCustomer)(session.accessToken);\n        return {\n            customer,\n            address: result.customerAddress\n        };\n    } catch (error) {\n        console.error(\"Error adding customer address:\", error);\n        throw error;\n    }\n}\n/**\n * Update an existing customer address\n * @param id Address ID\n * @param address Address data\n * @returns Updated customer data\n */ async function updateCustomerAddress(id, address) {\n    try {\n        const session = getCustomerSession();\n        if (!session) {\n            throw new Error(\"No active customer session found\");\n        }\n        const result = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.updateAddress)(session.accessToken, id, address);\n        if (result.customerUserErrors && result.customerUserErrors.length > 0) {\n            throw new Error(result.customerUserErrors[0].message);\n        }\n        // Get updated customer data\n        const customer = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.getCustomer)(session.accessToken);\n        return {\n            customer,\n            address: result.customerAddress\n        };\n    } catch (error) {\n        console.error(\"Error updating customer address:\", error);\n        throw error;\n    }\n}\n/**\n * Delete a customer address\n * @param id Address ID\n * @returns Updated customer data\n */ async function deleteCustomerAddress(id) {\n    try {\n        const session = getCustomerSession();\n        if (!session) {\n            throw new Error(\"No active customer session found\");\n        }\n        const result = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.deleteAddress)(session.accessToken, id);\n        if (result.customerUserErrors && result.customerUserErrors.length > 0) {\n            throw new Error(result.customerUserErrors[0].message);\n        }\n        // Get updated customer data\n        const customer = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.getCustomer)(session.accessToken);\n        return {\n            customer,\n            deletedAddressId: result.deletedCustomerAddressId\n        };\n    } catch (error) {\n        console.error(\"Error deleting customer address:\", error);\n        throw error;\n    }\n}\n/**\n * Set a default address for the customer\n * @param addressId Address ID\n * @returns Updated customer data\n */ async function setCustomerDefaultAddress(addressId) {\n    try {\n        const session = getCustomerSession();\n        if (!session) {\n            throw new Error(\"No active customer session found\");\n        }\n        const result = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.setDefaultAddress)(session.accessToken, addressId);\n        if (result.customerUserErrors && result.customerUserErrors.length > 0) {\n            throw new Error(result.customerUserErrors[0].message);\n        }\n        // Get updated customer data\n        const customer = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.getCustomer)(session.accessToken);\n        return {\n            customer\n        };\n    } catch (error) {\n        console.error(\"Error setting default address:\", error);\n        throw error;\n    }\n}\n/**\n * Logout the current customer\n */ function logoutCustomer() {\n    if (typeof localStorage !== \"undefined\") {\n        localStorage.removeItem(AUTH_TOKEN_KEY);\n        localStorage.removeItem(REFRESH_TOKEN_KEY);\n    }\n    // Also clear cookies\n    deleteCookie(AUTH_TOKEN_KEY);\n    deleteCookie(REFRESH_TOKEN_KEY);\n}\n/**\n * Delete cookie by name\n */ function deleteCookie(name) {\n    if (typeof document !== \"undefined\") {\n        document.cookie = \"\".concat(name, \"=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;\");\n    }\n}\n/**\n * Get the current customer session from cookies\n * @returns Customer session if active, null otherwise\n */ function getCustomerSession() {\n    try {\n        if (typeof document === \"undefined\") {\n            return null;\n        }\n        const token = getCookie(AUTH_TOKEN_KEY);\n        if (!token) {\n            return null;\n        }\n        // For now, we'll create a basic session object with the token\n        // In a real implementation, you might want to decode the JWT to get expiration\n        return {\n            accessToken: token,\n            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours from now\n        };\n    } catch (error) {\n        console.error(\"Error getting customer session:\", error);\n        return null;\n    }\n}\n/**\n * Get cookie value by name\n */ function getCookie(name) {\n    if (typeof document === \"undefined\") return null;\n    const nameEQ = \"\".concat(name, \"=\");\n    const ca = document.cookie.split(\";\");\n    for(let i = 0; i < ca.length; i++){\n        let c = ca[i];\n        while(c.charAt(0) === \" \")c = c.substring(1, c.length);\n        if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);\n    }\n    return null;\n}\n/**\n * Save customer token to localStorage and cookies\n * @param session Customer session to save\n */ function saveCustomerToken(session) {\n    if (typeof localStorage !== \"undefined\") {\n        localStorage.setItem(AUTH_TOKEN_KEY, JSON.stringify(session));\n    }\n    // Also save to cookies for consistency with clientAuth\n    setCookie(AUTH_TOKEN_KEY, session.accessToken);\n}\n/**\n * Set cookie with name and value\n */ function setCookie(name, value) {\n    let days = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 30;\n    if (typeof document !== \"undefined\") {\n        const secure =  false ? 0 : \"\";\n        const date = new Date();\n        date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);\n        const expires = \"; expires=\" + date.toUTCString();\n        document.cookie = \"\".concat(name, \"=\").concat(value).concat(expires, \"; path=/; SameSite=Lax\").concat(secure);\n    }\n}\n/**\n * Check if customer is logged in\n * @returns true if customer is logged in, false otherwise\n */ function isCustomerLoggedIn() {\n    return getCustomerSession() !== null;\n}\n/**\n * Get the current customer data\n * @returns Customer data if logged in, null otherwise\n */ async function getCurrentCustomer() {\n    try {\n        const session = getCustomerSession();\n        if (!session) {\n            return null;\n        }\n        const customer = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.getCustomer)(session.accessToken);\n        return customer;\n    } catch (error) {\n        console.error(\"Error getting current customer:\", error);\n        // If there's an error, the token might be invalid\n        logoutCustomer();\n        return null;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/auth.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/clientAuth.ts":
/*!*******************************!*\
  !*** ./src/lib/clientAuth.ts ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkAuth: function() { return /* binding */ checkAuth; },\n/* harmony export */   getAuthToken: function() { return /* binding */ getAuthToken; },\n/* harmony export */   getCurrentCustomer: function() { return /* binding */ getCurrentCustomer; },\n/* harmony export */   getCurrentUser: function() { return /* binding */ getCurrentUser; },\n/* harmony export */   getRefreshToken: function() { return /* binding */ getRefreshToken; },\n/* harmony export */   login: function() { return /* binding */ login; },\n/* harmony export */   logout: function() { return /* binding */ logout; },\n/* harmony export */   refreshToken: function() { return /* binding */ refreshToken; },\n/* harmony export */   register: function() { return /* binding */ register; },\n/* harmony export */   updateCustomer: function() { return /* binding */ updateCustomer; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var graphql_request__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! graphql-request */ \"(app-pages-browser)/./node_modules/graphql-request/build/entrypoints/main.js\");\n/* harmony import */ var jwt_decode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! jwt-decode */ \"(app-pages-browser)/./node_modules/jwt-decode/build/esm/index.js\");\n/**\n * Client-side authentication module that works with the WooCommerce authentication API\n */ \nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        '\\n  mutation LoginUser($username: String!, $password: String!) {\\n    login(input: {\\n      clientMutationId: \"login\"\\n      username: $username\\n      password: $password\\n    }) {\\n      authToken\\n      refreshToken\\n      user {\\n        id\\n        databaseId\\n        email\\n        firstName\\n        lastName\\n      }\\n    }\\n  }\\n'\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation RegisterUser($input: RegisterCustomerInput!) {\\n    registerCustomer(input: $input) {\\n      clientMutationId\\n      authToken\\n      refreshToken\\n      customer {\\n        id\\n        databaseId\\n        email\\n        firstName\\n        lastName\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject2() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation RefreshAuthToken($input: RefreshJwtAuthTokenInput!) {\\n    refreshJwtAuthToken(input: $input) {\\n      authToken\\n    }\\n  }\\n\"\n    ]);\n    _templateObject2 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject3() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetCustomer {\\n    customer {\\n      id\\n      databaseId\\n      email\\n      firstName\\n      lastName\\n      billing {\\n        firstName\\n        lastName\\n        company\\n        address1\\n        address2\\n        city\\n        state\\n        postcode\\n        country\\n        email\\n        phone\\n      }\\n      shipping {\\n        firstName\\n        lastName\\n        company\\n        address1\\n        address2\\n        city\\n        state\\n        postcode\\n        country\\n      }\\n      orders {\\n        nodes {\\n          id\\n          databaseId\\n          date\\n          status\\n          total\\n          lineItems {\\n            nodes {\\n              product {\\n                node {\\n                  id\\n                  name\\n                }\\n              }\\n              quantity\\n              total\\n            }\\n          }\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject3 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject4() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation UpdateCustomer($input: UpdateCustomerInput!) {\\n    updateCustomer(input: $input) {\\n      clientMutationId\\n      customer {\\n        id\\n        databaseId\\n        email\\n        firstName\\n        lastName\\n        displayName\\n        billing {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n          email\\n          phone\\n        }\\n        shipping {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject4 = function() {\n        return data;\n    };\n    return data;\n}\n\n\n\n// Auth token cookie name\nconst AUTH_COOKIE_NAME = \"woo_auth_token\";\nconst REFRESH_COOKIE_NAME = \"woo_refresh_token\";\n// Login mutation\nconst LOGIN_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject());\n// Register mutation\nconst REGISTER_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject1());\n// Refresh token mutation\nconst REFRESH_TOKEN_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject2());\n// Get customer query\nconst GET_CUSTOMER_QUERY = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject3());\n// Update customer mutation\nconst UPDATE_CUSTOMER_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject4());\nconst endpoint = \"https://deepskyblue-penguin-370791.hostingersite.com/graphql\" || 0 || 0;\n// Make sure the URL has https:// prefix\nconst formattedEndpoint = endpoint && !endpoint.startsWith(\"http\") ? \"https://\".concat(endpoint) : endpoint;\nconst graphQLClient = new graphql_request__WEBPACK_IMPORTED_MODULE_1__.GraphQLClient(formattedEndpoint, {\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Client-side cookie utilities\nfunction setCookie(name, value) {\n    let days = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 30;\n    // Use SameSite=Lax for better security while allowing normal navigation\n    // Secure flag should only be used in production (HTTPS)\n    const secure =  false ? 0 : \"\";\n    const date = new Date();\n    date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);\n    const expires = \"; expires=\" + date.toUTCString();\n    document.cookie = \"\".concat(name, \"=\").concat(value).concat(expires, \"; path=/; SameSite=Lax\").concat(secure);\n}\nfunction getCookie(name) {\n    if (typeof document === \"undefined\") return null;\n    const nameEQ = \"\".concat(name, \"=\");\n    const ca = document.cookie.split(\";\");\n    for(let i = 0; i < ca.length; i++){\n        let c = ca[i];\n        while(c.charAt(0) === \" \")c = c.substring(1, c.length);\n        if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);\n    }\n    return null;\n}\nfunction deleteCookie(name) {\n    if (typeof document === \"undefined\") return;\n    document.cookie = \"\".concat(name, \"=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;SameSite=Lax\");\n}\n/**\n * Login user with username/email and password\n */ async function login(username, password) {\n    try {\n        const response = await fetch(\"/api/auth\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                action: \"login\",\n                username,\n                password\n            }),\n            credentials: \"include\"\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || \"Login failed\");\n        }\n        const data = await response.json();\n        // Update auth state based on response success\n        if (data.success && data.user) {\n            // Store login time for session tracking\n            if (typeof localStorage !== \"undefined\") {\n                localStorage.setItem(\"auth_session_started\", Date.now().toString());\n            }\n            console.log(\"Login successful, user data received\");\n            return {\n                success: true,\n                user: data.user,\n                token: data.token // Include token from API response\n            };\n        } else {\n            console.error(\"Login response missing user data\");\n            throw new Error(\"Login failed: Invalid response from server\");\n        }\n    } catch (error) {\n        console.error(\"Login error:\", error);\n        return {\n            success: false,\n            message: error.message || \"Login failed\"\n        };\n    }\n}\n/**\n * Register a new user\n */ async function register(email, firstName, lastName, password) {\n    try {\n        const response = await fetch(\"/api/auth\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                action: \"register\",\n                email,\n                firstName,\n                lastName,\n                password\n            }),\n            credentials: \"include\"\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || \"Registration failed\");\n        }\n        const data = await response.json();\n        // Check for success and customer data\n        if (data.success && data.customer) {\n            // Store login time for session tracking\n            if (typeof localStorage !== \"undefined\") {\n                localStorage.setItem(\"auth_session_started\", Date.now().toString());\n            }\n            console.log(\"Registration successful, user data received\");\n            return {\n                success: true,\n                customer: data.customer,\n                token: data.token // Include token from API response\n            };\n        } else {\n            console.error(\"Registration response missing customer data\");\n            throw new Error(\"Registration failed: Invalid response from server\");\n        }\n    } catch (error) {\n        console.error(\"Registration error:\", error);\n        return {\n            success: false,\n            message: error.message || \"Registration failed\"\n        };\n    }\n}\n/**\n * Logout the current user\n */ async function logout() {\n    try {\n        // Make server request to clear cookies\n        const response = await fetch(\"/api/auth\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                action: \"logout\"\n            }),\n            credentials: \"include\"\n        });\n        // Clear any client-side storage\n        if (typeof localStorage !== \"undefined\") {\n            localStorage.removeItem(\"auth_session_started\");\n        }\n        // Delete cookies client-side as well\n        deleteCookie(AUTH_COOKIE_NAME);\n        deleteCookie(REFRESH_COOKIE_NAME);\n        console.log(\"Logout successful, session cleared\");\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error(\"Logout error:\", error);\n        // Still clear local storage and cookies even if server request fails\n        if (typeof localStorage !== \"undefined\") {\n            localStorage.removeItem(\"auth_session_started\");\n        }\n        deleteCookie(AUTH_COOKIE_NAME);\n        deleteCookie(REFRESH_COOKIE_NAME);\n        return {\n            success: true\n        }; // Return success even if API call fails\n    }\n}\n/**\n * Check if the user is authenticated\n */ async function checkAuth() {\n    try {\n        const token = getAuthToken();\n        if (!token) {\n            return false;\n        }\n        // Check if token is expired\n        try {\n            const decoded = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_2__.jwtDecode)(token);\n            const currentTime = Date.now() / 1000;\n            if (decoded.exp < currentTime) {\n                // Token is expired, try to refresh\n                const refreshed = await refreshToken();\n                return refreshed.success;\n            }\n        } catch (e) {\n            console.error(\"Error decoding token:\", e);\n            return false;\n        }\n        // Construct absolute URL for API request\n        const baseUrl =  true ? window.location.origin : 0;\n        // Verify token with server\n        const response = await fetch(\"\".concat(baseUrl, \"/api/auth/user\"), {\n            headers: {\n                Authorization: \"Bearer \".concat(token),\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\" // Include credentials (cookies)\n        });\n        if (!response.ok) {\n            console.log(\"Auth check failed with status:\", response.status);\n            return false;\n        }\n        const data = await response.json();\n        return data.success === true;\n    } catch (error) {\n        console.error(\"Auth check error:\", error);\n        return false;\n    }\n}\n/**\n * Refresh authentication token\n */ async function refreshToken() {\n    try {\n        const response = await fetch(\"/api/auth\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                action: \"refresh\"\n            })\n        });\n        const data = await response.json();\n        if (!response.ok) {\n            throw new Error(data.message || \"Token refresh failed\");\n        }\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error(\"Token refresh error:\", error);\n        return {\n            success: false,\n            message: error.message || \"Token refresh failed\"\n        };\n    }\n}\n/**\n * Get current user data\n */ async function getCurrentUser() {\n    try {\n        const response = await fetch(\"/api/auth/user\", {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (response.status === 401) {\n            return null;\n        }\n        const data = await response.json();\n        if (!response.ok || !data.success) {\n            return null;\n        }\n        return data.user;\n    } catch (error) {\n        console.error(\"Get user error:\", error);\n        return null;\n    }\n}\n/**\n * Get current customer data\n */ async function getCurrentCustomer() {\n    const token = getCookie(AUTH_COOKIE_NAME);\n    if (!token) {\n        return {\n            success: false,\n            message: \"Not authenticated\"\n        };\n    }\n    try {\n        // Set auth header\n        const client = new graphql_request__WEBPACK_IMPORTED_MODULE_1__.GraphQLClient(endpoint, {\n            headers: {\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        const data = await client.request(GET_CUSTOMER_QUERY);\n        if (!data.customer) {\n            throw new Error(\"Failed to fetch customer data\");\n        }\n        return {\n            success: true,\n            customer: normalizeCustomer(data.customer)\n        };\n    } catch (error) {\n        var _error_message;\n        console.error(\"Get customer error:\", error);\n        // If token expired, try to refresh\n        if ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"jwt expired\")) {\n            const refreshed = await refreshToken();\n            if (refreshed.success) {\n                return getCurrentCustomer();\n            }\n        }\n        return {\n            success: false,\n            message: error.message || \"Failed to fetch customer data\"\n        };\n    }\n}\n/**\n * Update customer data\n */ async function updateCustomer(customerData) {\n    const token = getCookie(AUTH_COOKIE_NAME);\n    if (!token) {\n        throw new Error(\"Not authenticated\");\n    }\n    try {\n        // Set auth header\n        const client = new graphql_request__WEBPACK_IMPORTED_MODULE_1__.GraphQLClient(endpoint, {\n            headers: {\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        // Prepare input data\n        const input = {\n            clientMutationId: \"updateCustomer\",\n            firstName: customerData.firstName,\n            lastName: customerData.lastName,\n            email: customerData.email,\n            billing: customerData.billing,\n            shipping: customerData.shipping\n        };\n        const data = await client.request(UPDATE_CUSTOMER_MUTATION, {\n            input\n        });\n        if (!data.updateCustomer || !data.updateCustomer.customer) {\n            throw new Error(\"Failed to update customer data\");\n        }\n        return {\n            success: true,\n            customer: data.updateCustomer.customer\n        };\n    } catch (error) {\n        console.error(\"Update customer error:\", error);\n        throw new Error(error.message || \"Failed to update customer data\");\n    }\n}\n/**\n * Get auth token from cookies\n */ function getAuthToken() {\n    return getCookie(AUTH_COOKIE_NAME);\n}\n/**\n * Get refresh token from cookies\n */ function getRefreshToken() {\n    return getCookie(REFRESH_COOKIE_NAME);\n}\n/**\n * Normalize customer data\n */ function normalizeCustomer(customer) {\n    return {\n        id: customer.id || \"\",\n        databaseId: customer.databaseId || 0,\n        email: customer.email || \"\",\n        firstName: customer.firstName || \"\",\n        lastName: customer.lastName || \"\",\n        displayName: customer.displayName || \"\".concat(customer.firstName || \"\", \" \").concat(customer.lastName || \"\").trim(),\n        billing: customer.billing || null,\n        shipping: customer.shipping || null,\n        orders: customer.orders || null\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/clientAuth.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/currency.ts":
/*!*****************************!*\
  !*** ./src/lib/currency.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_CURRENCY_CODE: function() { return /* binding */ DEFAULT_CURRENCY_CODE; },\n/* harmony export */   DEFAULT_CURRENCY_SYMBOL: function() { return /* binding */ DEFAULT_CURRENCY_SYMBOL; },\n/* harmony export */   formatPrice: function() { return /* binding */ formatPrice; },\n/* harmony export */   formatPriceWithoutSymbol: function() { return /* binding */ formatPriceWithoutSymbol; },\n/* harmony export */   getCurrencySymbol: function() { return /* binding */ getCurrencySymbol; }\n/* harmony export */ });\n/**\r\n * Currency utility functions for Ankkor\r\n */ /**\r\n * Format a numeric price to a currency string\r\n */ function formatPrice(amount) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { locale = \"en-IN\", currency = \"INR\", minimumFractionDigits = 0, maximumFractionDigits = 2 } = options;\n    const numericAmount = typeof amount === \"string\" ? parseFloat(amount) : amount;\n    return new Intl.NumberFormat(locale, {\n        style: \"currency\",\n        currency,\n        minimumFractionDigits,\n        maximumFractionDigits\n    }).format(numericAmount);\n}\n/**\r\n * Get currency symbol for a given currency code\r\n */ function getCurrencySymbol() {\n    let currencyCode = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"INR\", locale = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"en-IN\";\n    return 0..toLocaleString(locale, {\n        style: \"currency\",\n        currency: currencyCode,\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 0\n    }).replace(/\\d/g, \"\").trim();\n}\n/**\r\n * Format price without currency symbol\r\n */ function formatPriceWithoutSymbol(amount) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { locale = \"en-IN\", minimumFractionDigits = 0, maximumFractionDigits = 2 } = options;\n    const numericAmount = typeof amount === \"string\" ? parseFloat(amount) : amount;\n    return new Intl.NumberFormat(locale, {\n        style: \"decimal\",\n        minimumFractionDigits,\n        maximumFractionDigits\n    }).format(numericAmount);\n}\n/**\r\n * Default currency symbol for the application\r\n */ const DEFAULT_CURRENCY_SYMBOL = \"₹\";\nconst DEFAULT_CURRENCY_CODE = \"INR\";\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/currency.ts\n"));

/***/ })

}]);